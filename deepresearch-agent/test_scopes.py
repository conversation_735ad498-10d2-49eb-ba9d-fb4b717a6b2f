#!/usr/bin/env python3
"""
Test different OAuth scopes for Google Sheets access
"""

import os
import json
from google.oauth2 import service_account
from googleapiclient.discovery import build
from dotenv import load_dotenv

def test_with_scopes(scopes, scope_name):
    """Test Google Sheets access with specific scopes"""
    print(f"\n🧪 Testing with {scope_name}:")
    print(f"   Scopes: {scopes}")
    
    load_dotenv()
    service_account_path = os.getenv("SERVICE_ACCOUNT_PATH")
    
    try:
        # Create credentials with specific scopes
        credentials = service_account.Credentials.from_service_account_file(
            service_account_path,
            scopes=scopes
        )
        
        # Test Google Sheets API
        sheets_service = build('sheets', 'v4', credentials=credentials)
        
        # Try to create a simple spreadsheet
        spreadsheet = {
            'properties': {
                'title': f'Scope Test - {scope_name} - Safe to Delete'
            }
        }
        
        result = sheets_service.spreadsheets().create(body=spreadsheet).execute()
        spreadsheet_id = result.get('spreadsheetId')
        
        print(f"✅ SUCCESS! Created spreadsheet: {spreadsheet_id}")
        
        # Clean up
        drive_service = build('drive', 'v3', credentials=credentials)
        drive_service.files().delete(fileId=spreadsheet_id).execute()
        print(f"✅ Cleaned up test spreadsheet")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED: {e}")
        return False

def main():
    """Test different scope combinations"""
    print("🔍 Testing Different OAuth Scopes for Google Sheets")
    print("=" * 60)
    
    # Different scope combinations to test
    scope_tests = [
        {
            'name': 'Standard Scopes',
            'scopes': [
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive'
            ]
        },
        {
            'name': 'Full Drive Access',
            'scopes': [
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive'
            ]
        },
        {
            'name': 'Broader Scopes',
            'scopes': [
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/cloud-platform'
            ]
        },
        {
            'name': 'All Google APIs',
            'scopes': [
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive',
                'https://www.googleapis.com/auth/cloud-platform',
                'https://www.googleapis.com/auth/devstorage.full_control'
            ]
        },
        {
            'name': 'Minimal Sheets Only',
            'scopes': [
                'https://www.googleapis.com/auth/spreadsheets'
            ]
        }
    ]
    
    successful_scopes = []
    
    for test in scope_tests:
        success = test_with_scopes(test['scopes'], test['name'])
        if success:
            successful_scopes.append(test)
    
    print("\n" + "=" * 60)
    print("📋 RESULTS SUMMARY:")
    
    if successful_scopes:
        print(f"✅ {len(successful_scopes)} scope combination(s) worked:")
        for scope_test in successful_scopes:
            print(f"   - {scope_test['name']}")
    else:
        print("❌ No scope combinations worked!")
        print("This suggests a deeper permission or configuration issue.")
        
        print("\n🔧 Additional troubleshooting steps:")
        print("1. Verify the service account key file is not corrupted")
        print("2. Check if the service account was created correctly")
        print("3. Ensure the Google Cloud project has billing enabled")
        print("4. Try creating a new service account")
        print("5. Check Google Cloud Console audit logs for detailed errors")

if __name__ == "__main__":
    main()
