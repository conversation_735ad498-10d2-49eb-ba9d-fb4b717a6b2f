#!/usr/bin/env python3
"""
Google Sheets API Access Test Script

This script validates your Google Sheets and Drive API configuration
before using it with the deepresearch-agent MCP integration.
"""

import os
import json
from google.oauth2 import service_account as sa_module
from googleapiclient.discovery import build
from dotenv import load_dotenv

def test_google_access():
    """Test Google Sheets and Drive API access with current configuration"""
    try:
        # Load environment variables
        load_dotenv()
        
        # Check environment variables
        service_account_path = os.getenv("SERVICE_ACCOUNT_PATH")
        drive_folder_id = os.getenv("DRIVE_FOLDER_ID")
        
        print("🔍 Checking configuration...")
        print(f"Service Account Path: {service_account_path}")
        print(f"Drive Folder ID: {drive_folder_id}")
        
        if not service_account_path or not drive_folder_id:
            print("❌ Missing environment variables")
            print("Please check your .env file contains:")
            print("  SERVICE_ACCOUNT_PATH=/path/to/service-account.json")
            print("  DRIVE_FOLDER_ID=your_folder_id")
            return False
            
        # Check if service account file exists
        if not os.path.exists(service_account_path):
            print(f"❌ Service account file not found: {service_account_path}")
            print("Please check the file path and ensure the file exists")
            return False
            
        # Load and validate service account JSON
        try:
            with open(service_account_path, 'r') as f:
                sa_info = json.load(f)
        except json.JSONDecodeError as e:
            print(f"❌ Invalid JSON in service account file: {e}")
            return False
            
        client_email = sa_info.get('client_email')
        if not client_email:
            print("❌ Service account file missing 'client_email' field")
            return False
            
        print(f"✅ Service account email: {client_email}")
        print("📝 Make sure this email has access to your Google Drive folder!")
        
        # Create credentials
        print("\n🔐 Creating credentials...")
        credentials = sa_module.Credentials.from_service_account_file(
            service_account_path,
            scopes=[
                'https://www.googleapis.com/auth/spreadsheets',
                'https://www.googleapis.com/auth/drive'
            ]
        )
        
        # Test Google Sheets API
        print("📊 Testing Google Sheets API...")
        sheets_service = build('sheets', 'v4', credentials=credentials)
        print("✅ Google Sheets API connection successful")
        
        # Test Google Drive API
        print("💾 Testing Google Drive API...")
        drive_service = build('drive', 'v3', credentials=credentials)
        print("✅ Google Drive API connection successful")
        
        # Test folder access
        print(f"📁 Testing access to folder: {drive_folder_id}")
        try:
            folder_info = drive_service.files().get(fileId=drive_folder_id).execute()
            folder_name = folder_info.get('name', 'Unknown')
            print(f"✅ Folder access successful: '{folder_name}'")
        except Exception as e:
            print(f"❌ Cannot access folder: {e}")
            print("Please ensure:")
            print("  1. The folder ID is correct")
            print(f"  2. The folder is shared with: {client_email}")
            print("  3. The service account has 'Editor' permissions")
            return False
        
        # Check current service account permissions
        print("\n🔍 Checking service account permissions...")
        try:
            # Try to get service account info
            import googleapiclient.discovery

            # Get project ID from credentials
            project_id = credentials.service_account_email.split('@')[1].split('.')[0]
            print(f"📋 Project ID: {project_id}")

            # Check if we can list our own permissions (this requires specific roles)
            try:
                iam_service = build('cloudresourcemanager', 'v1', credentials=credentials)
                policy = iam_service.projects().getIamPolicy(
                    resource=project_id,
                    body={}
                ).execute()

                # Find our service account in the policy
                our_email = credentials.service_account_email
                our_roles = []

                for binding in policy.get('bindings', []):
                    if f'serviceAccount:{our_email}' in binding.get('members', []):
                        our_roles.extend(binding.get('role', '').split(','))

                if our_roles:
                    print(f"📋 Current roles: {', '.join(our_roles)}")
                else:
                    print("⚠️ Could not determine current roles")

            except Exception as perm_e:
                print(f"⚠️ Could not check permissions: {perm_e}")

        except Exception as check_e:
            print(f"⚠️ Permission check failed: {check_e}")

        # Test creating a simple spreadsheet
        print("\n📝 Testing spreadsheet creation...")

        # Try different approaches to create spreadsheet
        approaches = [
            {
                'name': 'Direct creation',
                'body': {
                    'properties': {
                        'title': 'DeepResearch API Test - Safe to Delete'
                    }
                }
            },
            {
                'name': 'Creation with explicit permissions',
                'body': {
                    'properties': {
                        'title': 'DeepResearch API Test - Safe to Delete'
                    },
                    'sheets': [{
                        'properties': {
                            'title': 'Sheet1'
                        }
                    }]
                }
            }
        ]

        spreadsheet_id = None
        creation_successful = False

        for approach in approaches:
            try:
                print(f"🧪 Trying: {approach['name']}")
                result = sheets_service.spreadsheets().create(body=approach['body']).execute()
                spreadsheet_id = result.get('spreadsheetId')
                spreadsheet_url = f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}"
                print(f"✅ Test spreadsheet created: {spreadsheet_id}")
                print(f"🔗 URL: {spreadsheet_url}")
                creation_successful = True
                break

            except Exception as e:
                print(f"❌ {approach['name']} failed: {e}")
                if "does not have permission" in str(e):
                    print("💡 This suggests missing Google Sheets API permissions")
                elif "quotaExceeded" in str(e):
                    print("💡 This suggests API quota limits")
                elif "forbidden" in str(e).lower():
                    print("💡 This suggests authentication or authorization issues")
                continue

        if not creation_successful:
            print("\n❌ All spreadsheet creation methods failed!")
            print("\n🔧 Suggested fixes:")
            print("1. Check Google Cloud Console > IAM & Admin > IAM")
            print("2. Find your service account and add these roles:")
            print("   - roles/sheets.editor (Google Sheets Editor)")
            print("   - roles/drive.file (Google Drive File Creator)")
            print("3. Wait 5-10 minutes for changes to propagate")
            print("4. Alternatively, try adding broader roles:")
            print("   - roles/editor (Editor)")
            return False

        # If creation was successful, continue with tests
        try:
            # Move to target folder
            print("📂 Moving spreadsheet to target folder...")
            drive_service.files().update(
                fileId=spreadsheet_id,
                addParents=drive_folder_id,
                removeParents='root'
            ).execute()
            print("✅ Spreadsheet moved to target folder")

            # Add some test data
            print("📊 Adding test data...")
            values = [
                ['Column 1', 'Column 2', 'Column 3'],
                ['Test Data 1', 'Test Data 2', 'Test Data 3'],
                ['Row 2 Col 1', 'Row 2 Col 2', 'Row 2 Col 3']
            ]

            body = {
                'values': values
            }

            sheets_service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range='A1:C3',
                valueInputOption='RAW',
                body=body
            ).execute()
            print("✅ Test data added successfully")

            # Clean up - delete test spreadsheet
            print("🧹 Cleaning up test spreadsheet...")
            drive_service.files().delete(fileId=spreadsheet_id).execute()
            print("✅ Test spreadsheet cleaned up")

        except Exception as e:
            print(f"⚠️ Post-creation operations failed: {e}")
            if spreadsheet_id:
                print(f"📝 Manual cleanup needed: https://docs.google.com/spreadsheets/d/{spreadsheet_id}")
            # Don't return False here since creation worked
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        print("Full error details:")
        traceback.print_exc()
        return False

def main():
    """Main function to run the test"""
    print("=" * 60)
    print("🧪 Google Sheets API Access Test")
    print("=" * 60)
    print()
    
    success = test_google_access()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ALL TESTS PASSED!")
        print("Your Google Sheets configuration is working correctly.")
        print("You can now use the deepresearch-agent with Google Sheets integration.")
    else:
        print("💥 TESTS FAILED!")
        print("Please review the errors above and check the troubleshooting guide.")
        print("See TROUBLESHOOTING.md for detailed solutions.")
    print("=" * 60)

if __name__ == "__main__":
    main()
