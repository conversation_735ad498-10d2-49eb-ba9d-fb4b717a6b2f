# Google Sheets MCP Configuration
# Path to your Google Service Account <PERSON><PERSON><PERSON> file
# WORKING SERVICE ACCOUNT (can read/write existing sheets, but not create new ones):
SERVICE_ACCOUNT_PATH=/home/<USER>/samples/deepresearch-agent/deepresearch-sheets-v2-key.json

# Google Drive folder ID where sheets will be created
# You can get this from the URL when viewing a folder in Google Drive
# Example: https://drive.google.com/drive/folders/1ABC123DEF456GHI789JKL
# The folder ID would be: 1ABC123DEF456GHI789JKL
DRIVE_FOLDER_ID=1fwjSjY5vzQOX4VjLJvSTxwNDJNa5yz0a

# NOTE: Due to organizational policies, service accounts cannot CREATE new spreadsheets
# but can READ and WRITE to existing ones. Create template spreadsheets manually first.

# AWS Bedrock Configuration (if needed)
AWS_PROFILE=default
AWS_REGION=us-west-2

# Tavily API Key (for web search)
TAVILY_API_KEY=your_tavily_api_key_here
