# Google Sheets MCP Configuration
# Path to your Google Service Account JSO<PERSON> file
SERVICE_ACCOUNT_PATH=/path/to/your/service-account.json

# Google Drive folder ID where sheets will be created
# You can get this from the URL when viewing a folder in Google Drive
# Example: https://drive.google.com/drive/folders/1ABC123DEF456GHI789JKL
# The folder ID would be: 1ABC123DEF456GHI789JKL
DRIVE_FOLDER_ID=your_drive_folder_id_here

# AWS Bedrock Configuration (if needed)
AWS_PROFILE=default
AWS_REGION=us-west-2

# Tavily API Key (for web search)
TAVILY_API_KEY=your_tavily_api_key_here
