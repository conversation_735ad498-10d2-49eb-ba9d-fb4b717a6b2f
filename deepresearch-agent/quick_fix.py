#!/usr/bin/env python3
"""
Quick Fix for Google Sheets Permission Error

This script specifically addresses the "The caller does not have permission" 
error when creating Google Sheets.
"""

import subprocess
import sys

def main():
    print("🚀 Quick Fix for Google Sheets Permission Error")
    print("=" * 50)
    
    # Your specific service account and project
    service_account_email = "<EMAIL>"
    project_id = "chat10000-402ac"
    
    print(f"📧 Service Account: {service_account_email}")
    print(f"📋 Project: {project_id}")
    
    # Check if gcloud is available
    try:
        subprocess.run(['gcloud', '--version'], capture_output=True, check=True)
        print("✅ Google Cloud CLI found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Google Cloud CLI not found")
        print("Please install: https://cloud.google.com/sdk/docs/install")
        return
    
    # Set the correct project
    try:
        print(f"\n🔧 Setting project to: {project_id}")
        subprocess.run(['gcloud', 'config', 'set', 'project', project_id], check=True)
        print("✅ Project set successfully")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to set project: {e}")
        return
    
    # Add the required roles
    roles_to_add = [
        'roles/sheets.editor',
        'roles/drive.file',
        'roles/editor'  # Broader role as fallback
    ]
    
    print(f"\n🔧 Adding roles to service account...")
    
    for role in roles_to_add:
        try:
            print(f"➕ Adding {role}...")
            result = subprocess.run([
                'gcloud', 'projects', 'add-iam-policy-binding', project_id,
                '--member', f'serviceAccount:{service_account_email}',
                '--role', role
            ], capture_output=True, text=True, check=True)
            
            print(f"✅ {role} added successfully")
            
        except subprocess.CalledProcessError as e:
            error_output = e.stderr if e.stderr else str(e)
            if isinstance(error_output, bytes):
                error_output = error_output.decode()
            if "already exists" in error_output or "already has" in error_output:
                print(f"ℹ️ {role} already exists")
            else:
                print(f"⚠️ Failed to add {role}: {error_output}")
                print(f"   Command: {' '.join(e.cmd)}")
                print(f"   Return code: {e.returncode}")
    
    # Verify the roles
    print(f"\n🔍 Verifying current roles...")
    try:
        result = subprocess.run([
            'gcloud', 'projects', 'get-iam-policy', project_id,
            '--flatten=bindings[].members',
            '--format=value(bindings.role)',
            f'--filter=bindings.members:serviceAccount:{service_account_email}'
        ], capture_output=True, text=True, check=True)
        
        roles = result.stdout.strip().split('\n') if result.stdout.strip() else []
        
        if roles:
            print("📋 Current roles:")
            for role in roles:
                if role:
                    print(f"  ✓ {role}")
        else:
            print("❌ No roles found!")
    
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to verify roles: {e}")
    
    # Enable APIs
    print(f"\n🔌 Ensuring APIs are enabled...")
    apis = ['sheets.googleapis.com', 'drive.googleapis.com']
    
    for api in apis:
        try:
            subprocess.run([
                'gcloud', 'services', 'enable', api, '--project', project_id
            ], check=True, capture_output=True)
            print(f"✅ {api} enabled")
        except subprocess.CalledProcessError as e:
            print(f"⚠️ Failed to enable {api}: {e}")
    
    print(f"\n" + "=" * 50)
    print("🎉 Quick fix completed!")
    print("⏰ Please wait 5-10 minutes for changes to propagate")
    print("🧪 Then run: python test_google_access.py")
    print("=" * 50)

if __name__ == "__main__":
    main()
