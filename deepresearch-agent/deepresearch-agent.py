import asyncio
import os
from strands import Agent
from strands_tools import calculator, current_time, stop, handoff_to_user, http_request, tavily
from strands.models import BedrockModel
from mcp import stdio_client, StdioServerParameters
from strands.tools.mcp import MCPClient

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 环境变量已从 .env 文件加载")
except ImportError:
    print("警告：未安装 python-dotenv。请使用以下命令安装：pip install python-dotenv")


# Initialize our agent without a callback handler
bedrock_model = BedrockModel(
    model_id="us.anthropic.claude-sonnet-4-********-v1:0",
    region_name="us-west-2",
    temperature=0.3,
    prompt_cache="default",
    tool_cache="default"
)

# Initialize Google Sheets MCP client
google_sheet_mcp_client = MCPClient(lambda: stdio_client(
    StdioServerParameters(
        command="uvx",
        args=["mcp-google-sheets@latest"],
        env={
            "SERVICE_ACCOUNT_PATH": os.getenv("SERVICE_ACCOUNT_PATH"),
            "DRIVE_FOLDER_ID": os.getenv("DRIVE_FOLDER_ID")
        }
    )
))

SOCIAL_MEDIA_ADVISOR='''你是一位资深自媒体运营助手，任务是帮助用户搜集当前热门话题与爆款标题，整理成可用于内容创作的 Excel 表格。

你现在具备以下能力：
1. 搜索和收集热门话题信息
2. 创建和管理 Google Sheets 电子表格
3. 将收集的数据直接保存到云端表格中

请根据以下主题关键词，搜索 Google、微博热搜、知乎热榜、B站热门、抖音热点等平台，提取以下内容：

关键词：{用户输入主题，如“AI副业”}

提取字段：
- 话题标题（原始）
- 平台来源（如微博、知乎、B站）
- 热度指标（如阅读量、讨论量、点赞数，尽量获取）
- 参考爆款标题（可选1-2个）
- 发布时间（如“2小时前”）
- 关键词标签（如“副业”、“AI工具”、“打工人”）

输出格式：
请生成一个 Google Sheets 表格，包含上述字段作为表头，并填充至少10条真实数据。表格命名为「热门话题_{关键词}_{日期}」。

示例输出（前2行）：

| 话题标题 | 平台 | 热度 | 爆款标题参考 | 发布时间 | 标签 |
|----------|------|------|----------------|------------|--------|
| “AI副业真的靠谱吗？” | 知乎 | 423万浏览 | “我靠AI写作月入8000，真相是…” | 6小时前 | AI副业、打工人 |
| “打工人下班做AI头像” | 小红书 | 8.2万赞 | “0成本AI头像副业，我一周赚300” | 1天前 | AI头像、副业 |

注意：
- 确保数据真实、来源可追溯，优先选择近期（7天内）内容
- 使用 Google Sheets 工具直接创建云端表格，方便用户访问和编辑
- 提供表格的分享链接给用户
'''

# Initialize agent with Google Sheets MCP tools
def initialize_agent():
    """Initialize the agent with Google Sheets MCP tools"""
    try:
        # Start the Google Sheets MCP client
        google_sheet_mcp_client.__enter__()

        # Get Google Sheets tools
        google_sheets_tools = google_sheet_mcp_client.list_tools_sync()

        # Combine all tools
        all_tools = [current_time, stop, handoff_to_user, tavily] + google_sheets_tools

        # Create agent with all tools
        agent = Agent(
            model=bedrock_model,
            system_prompt=SOCIAL_MEDIA_ADVISOR,
            tools=all_tools,
            callback_handler=None  # Disable default callback handler
        )

        print(f"✅ Agent initialized with {len(google_sheets_tools)} Google Sheets tools")
        return agent

    except Exception as e:
        print(f"⚠️ Failed to initialize Google Sheets MCP client: {e}")
        print("📝 Falling back to agent without Google Sheets tools")

        # Fallback: create agent without Google Sheets tools
        agent = Agent(
            model=bedrock_model,
            system_prompt=SOCIAL_MEDIA_ADVISOR,
            tools=[current_time, stop, handoff_to_user, tavily],
            callback_handler=None  # Disable default callback handler
        )
        return agent

# Initialize the agent
agent = initialize_agent()

# Async function that iterates over streamed agent events
async def deepresearch_agent(prompt):
    #prompt = "What is 25 * 48 and explain the calculation"

    # Get an async iterator for the agent's response stream
    agent_stream = agent.stream_async(prompt)

    # Process events as they arrive
    async for event in agent_stream:
        if "data" in event:
            # Print text chunks as they're generated
            print(event["data"], end="", flush=True)
        elif "current_tool_use" in event and event["current_tool_use"].get("name"):
            # Print tool usage information
            print(f"\n[Tool use delta for: {event['current_tool_use']['name']}]")

def cleanup_mcp_client():
    """Cleanup the Google Sheets MCP client"""
    try:
        google_sheet_mcp_client.__exit__(None, None, None)
        print("✅ Google Sheets MCP client cleaned up")
    except Exception as e:
        print(f"⚠️ Error cleaning up Google Sheets MCP client: {e}")

if __name__ == "__main__":

    print("\n👨‍🍳 DeepResearch: let me search anything for you Type 'exit' to quit.\n")
    print("📊 Google Sheets integration enabled - you can now create and manage spreadsheets!")
    print("🔧 Required environment variables: SERVICE_ACCOUNT_PATH, DRIVE_FOLDER_ID\n")

    try:
        # Run the agent in a loop for interactive conversation
        while True:
            user_input = input("\nYou > ")
            if user_input.lower() == "exit":
                print("Good Bye! 🍽️")
                break
            #response = deepresearch_agent(user_input)
            #print(f"\DeepResearch Agent > {response}")

            # Run the agent with the async event processing
            asyncio.run(deepresearch_agent(user_input))

    except KeyboardInterrupt:
        print("\n\n👋 Interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        # Cleanup MCP client
        cleanup_mcp_client()