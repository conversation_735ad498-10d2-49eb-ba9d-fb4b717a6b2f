"""
E-commerce Product Sourcing Agent

This agent helps users find hot-selling products on Amazon and corresponding suppliers on 1688 platform.
It provides comprehensive information including wholesale prices, supplier details, and shipping costs.

Architecture:
- Single agent using Strands framework
- Two custom tools: Amazon hot products API and 1688 supplier platform API
- Structured workflow for product sourcing and supplier matching
"""

import os
import json
import re
import requests
from datetime import datetime
from typing import Any, Dict, List, Optional
from dataclasses import dataclass

from strands import Agent, tool
from strands.models import BedrockModel
from strands.types.tools import ToolResult, ToolUse
import argparse
import json
from bedrock_agentcore.runtime import BedrockAgentCoreApp

app = BedrockAgentCoreApp()

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 环境变量已从 .env 文件加载")
except ImportError:
    print("警告：未安装 python-dotenv。请使用以下命令安装：pip install python-dotenv")

# SerpAPI for Amazon search integration
try:
    from serpapi import GoogleSearch
except ImportError:
    print("警告：未安装 serpapi。请使用以下命令安装：pip install google-search-results")
    GoogleSearch = None


# Global variable to maintain task directory state across debug exports
_current_task_directory = None


def _initialize_task_directory(base_export_dir: str = None) -> str:
    """
    Initialize a new task directory for the current task execution.
    This should be called once at the beginning of a task.

    Args:
        base_export_dir: Base directory for debug exports (uses env var if None)

    Returns:
        Path to the task-specific directory
    """
    global _current_task_directory

    if base_export_dir is None:
        base_export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")

    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    task_dir = os.path.join(base_export_dir, timestamp)
    os.makedirs(task_dir, exist_ok=True)

    _current_task_directory = task_dir
    return task_dir


def _get_current_task_directory(base_export_dir: str = None) -> str:
    """
    Get the current task directory. If none exists, create a new one.

    Args:
        base_export_dir: Base directory for debug exports (uses env var if None)

    Returns:
        Path to the current task directory
    """
    global _current_task_directory

    if _current_task_directory is None or not os.path.exists(_current_task_directory):
        return _initialize_task_directory(base_export_dir)

    return _current_task_directory


def _reset_task_directory():
    """
    Reset the task directory state. This can be called to start a new task.
    The next debug export will create a new task directory.
    """
    global _current_task_directory
    _current_task_directory = None


def _generate_task_directory(base_export_dir: str) -> str:
    """
    Generate or get the current task-specific directory with timestamp prefix.
    This function now uses the persistent task directory mechanism.

    Args:
        base_export_dir: Base directory for debug exports

    Returns:
        Path to the current task-specific directory
    """
    return _get_current_task_directory(base_export_dir)


def _export_api_response(response_data: Dict[str, Any], search_query: str, export_dir: str = None) -> str:
    """
    Export SerpAPI response to a JSON file for debugging purposes.

    Args:
        response_data: The raw SerpAPI response
        search_query: The search query used
        export_dir: Directory to save the export files (uses env var if None)

    Returns:
        Path to the exported file
    """
    # Check if export is enabled
    if not os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        return ""

    try:
        # Use environment variable for export directory if not specified
        if export_dir is None:
            base_export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
            export_dir = _get_current_task_directory(base_export_dir)

        # Generate filename with timestamp prefix
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        safe_query = re.sub(r'[^\w\s-]', '', search_query).strip()
        safe_query = re.sub(r'[-\s]+', '_', safe_query)
        filename = f"{timestamp}_serpapi_response_{safe_query}.json"
        filepath = os.path.join(export_dir, filename)

        # Prepare export data
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "search_query": search_query,
            "raw_response": response_data,
            "metadata": {
                "total_results": len(response_data.get("organic_results", [])),
                "has_error": "error" in response_data,
                "search_parameters": response_data.get("search_parameters", {}),
                "search_information": response_data.get("search_information", {})
            }
        }

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📁 API 响应已导出到：{filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️  导出 API 响应失败：{e}")
        return ""


def _export_processed_results(processed_products: List[Dict[str, Any]], search_query: str, export_dir: str = None) -> str:
    """
    Export processed product results to a JSON file for debugging purposes.

    Args:
        processed_products: The processed product list
        search_query: The search query used
        export_dir: Directory to save the export files (uses env var if None)

    Returns:
        Path to the exported file
    """
    # Check if export is enabled
    if not os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        return ""

    try:
        # Use environment variable for export directory if not specified
        if export_dir is None:
            base_export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
            export_dir = _get_current_task_directory(base_export_dir)

        # Generate filename with timestamp prefix
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        safe_query = re.sub(r'[^\w\s-]', '', search_query).strip()
        safe_query = re.sub(r'[-\s]+', '_', safe_query)
        filename = f"{timestamp}_processed_results_{safe_query}.json"
        filepath = os.path.join(export_dir, filename)

        # Prepare export data
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "search_query": search_query,
            "total_products": len(processed_products),
            "processed_products": processed_products,
            "summary": {
                "price_range": {
                    "min": min([p.get("price", 0) for p in processed_products]) if processed_products else 0,
                    "max": max([p.get("price", 0) for p in processed_products]) if processed_products else 0,
                    "avg": sum([p.get("price", 0) for p in processed_products]) / len(processed_products) if processed_products else 0
                },
                "rating_range": {
                    "min": min([p.get("rating", 0) for p in processed_products]) if processed_products else 0,
                    "max": max([p.get("rating", 0) for p in processed_products]) if processed_products else 0,
                    "avg": sum([p.get("rating", 0) for p in processed_products]) / len(processed_products) if processed_products else 0
                },
                "prime_products": len([p for p in processed_products if p.get("prime", False)]),
                "sponsored_products": len([p for p in processed_products if p.get("sponsored", False)])
            }
        }

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📊 处理结果已导出到：{filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️  导出处理结果失败：{e}")
        return ""


@dataclass
class ProductInfo:
    """Data class for product information"""
    title: str
    price: float
    rating: float
    reviews_count: int
    image_url: str
    product_url: str
    category: str
    sales_rank: Optional[int] = None
    keywords: List[str] = None


@dataclass
class SupplierInfo:
    """Data class for supplier information"""
    supplier_name: str
    company_name: str
    wholesale_price: float
    minimum_order_quantity: int
    supplier_rating: float
    years_in_business: int
    location: str
    contact_info: Dict[str, str]
    product_images: List[str]
    shipping_cost: float
    delivery_time: str
    certifications: List[str] = None


# Tool 1: Amazon Hot Products API Integration
@tool
def search_amazon_hot_products(
    product_category: str,
    keywords: str,
    price_range_min: float = 0.0,
    price_range_max: float = 1000.0,
    min_rating: float = 4.0,
    max_results: int = 10
) -> List[Dict[str, Any]]:
    """
    Search for hot-selling products on Amazon based on user requirements using SerpAPI.

    Args:
        product_category: Product category (e.g., "electronics", "home", "fashion")
        keywords: Search keywords describing the product
        price_range_min: Minimum price filter
        price_range_max: Maximum price filter
        min_rating: Minimum product rating filter
        max_results: Maximum number of results to return

    Returns:
        List of hot-selling products with details including price, rating, sales data
    """

    # Initialize task directory for this execution
    if os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        base_export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
        task_dir = _initialize_task_directory(base_export_dir)
        print(f"📁 任务目录已初始化：{task_dir}")

    print(f"🔍 正在搜索亚马逊热销产品：{keywords} 在 {product_category} 类别")
    print(f"   价格范围：${price_range_min} - ${price_range_max}")
    print(f"   最低评分：{min_rating}")

    # Check if mock mode is enabled
    use_mock_data = os.getenv("USE_MOCK_DATA", "false").lower() == "true"
    if use_mock_data:
        print("🎭 使用模拟数据模式")
        return _get_mock_amazon_products(keywords, product_category, price_range_min, price_range_max, min_rating, max_results)

    # Check if SerpAPI is available
    if GoogleSearch is None:
        print("⚠️  SerpAPI 不可用，无法进行真实 API 调用")
        return []

    try:
        # Get API key from environment variable
        api_key = os.getenv("SERPAPI_KEY")
        if not api_key:
            print("⚠️  未设置 SERPAPI_KEY 环境变量，无法进行真实 API 调用")
            return []

        # Prepare SerpAPI parameters
        search_query = f"{keywords} {product_category}".strip()
        params = {
            "engine": "amazon",
            "k": search_query,  # k parameter represents the product keyword
            "api_key": api_key,
            "num": min(max_results * 2, 20)  # Get more results to filter
        }

        # Execute search
        search = GoogleSearch(params)
        results = search.get_dict()

        # Export API response for debugging
        export_path = _export_api_response(results, search_query)

        # Check for errors
        if "error" in results:
            print(f"❌ SerpAPI 错误：{results['error']}")
            if export_path:
                print(f"   错误详情已保存到：{export_path}")
            return []

        # Extract organic results
        organic_results = results.get("organic_results", [])
        if not organic_results:
            print("⚠️  未找到有机搜索结果")
            return []

        # Transform SerpAPI results to our format
        transformed_products = []
        for result in organic_results:
            try:
                product = _transform_serpapi_result(result, product_category, keywords)
                if product and _meets_criteria(product, price_range_min, price_range_max, min_rating):
                    transformed_products.append(product)
                    if len(transformed_products) >= max_results:
                        break
            except Exception as e:
                print(f"⚠️  处理结果时出错：{e}")
                continue

        print(f"✅ 找到 {len(transformed_products)} 个符合条件的产品")

        # Export processed results for debugging
        if transformed_products:
            _export_processed_results(transformed_products, search_query)

        return transformed_products

    except Exception as e:
        print(f"❌ SerpAPI 搜索失败：{e}")
        return []


def _transform_serpapi_result(result: Dict[str, Any], product_category: str, keywords: str) -> Optional[Dict[str, Any]]:
    """
    Transform a SerpAPI result into our product format.

    Args:
        result: Single organic result from SerpAPI
        product_category: Product category for context
        keywords: Search keywords for context

    Returns:
        Transformed product dictionary or None if invalid
    """
    try:
        # Extract basic information
        title = result.get("title", "")
        if not title:
            return None

        # Extract price information
        price = result.get("extracted_price")
        if price is None:
            # Try to extract from price string
            price_str = result.get("price", "")
            if price_str:
                # Extract numeric value from price string like "$29.99"
                price_match = re.search(r'\$?(\d+\.?\d*)', price_str)
                if price_match:
                    price = float(price_match.group(1))
                else:
                    price = 0.0
            else:
                price = 0.0

        # Extract rating and reviews
        rating = result.get("rating", 0.0)
        reviews_count = result.get("reviews", 0)

        # Extract other information
        asin = result.get("asin", "")
        thumbnail = result.get("thumbnail", "")
        link_clean = result.get("link_clean", result.get("link", ""))

        # Extract sales indicators
        bought_last_month = result.get("bought_last_month", "")
        monthly_sales = _extract_monthly_sales(bought_last_month)

        # Determine trend based on available data
        trend = "stable"
        if "best seller" in title.lower() or "bestseller" in title.lower():
            trend = "increasing"
        elif monthly_sales > 1000:
            trend = "increasing"

        # Determine competition level based on reviews
        competition_level = "medium"
        if reviews_count > 5000:
            competition_level = "high"
        elif reviews_count < 500:
            competition_level = "low"

        # Build product dictionary
        product = {
            "title": title,
            "price": price,
            "rating": rating,
            "reviews_count": reviews_count,
            "image_url": thumbnail,
            "product_url": link_clean,
            "category": product_category,
            "asin": asin,
            "keywords": keywords.split(),
            "monthly_sales": monthly_sales,
            "trend": trend,
            "competition_level": competition_level,
            "bought_last_month": bought_last_month,
            "prime": result.get("prime", False),
            "sponsored": result.get("sponsored", False)
        }

        return product

    except Exception as e:
        print(f"⚠️  转换结果时出错：{e}")
        return None


def _extract_monthly_sales(bought_text: str) -> int:
    """
    Extract monthly sales number from text like "200+ bought in past month".

    Args:
        bought_text: Text containing sales information

    Returns:
        Estimated monthly sales number
    """
    if not bought_text:
        return 0

    # Look for patterns like "200+ bought", "1K+ bought", etc.
    match = re.search(r'(\d+(?:\.\d+)?)\s*([KkMm]?)\+?\s*bought', bought_text)
    if match:
        number = float(match.group(1))
        multiplier = match.group(2).upper()

        if multiplier == 'K':
            number *= 1000
        elif multiplier == 'M':
            number *= 1000000

        return int(number)

    return 0


def _meets_criteria(product: Dict[str, Any], price_min: float, price_max: float, min_rating: float) -> bool:
    """
    Check if a product meets the specified criteria.

    Args:
        product: Product dictionary
        price_min: Minimum price
        price_max: Maximum price
        min_rating: Minimum rating

    Returns:
        True if product meets criteria
    """
    price = product.get("price", 0)
    rating = product.get("rating", 0)

    return (price_min <= price <= price_max) and (rating >= min_rating)


def _get_mock_amazon_products(keywords: str, product_category: str, price_min: float, price_max: float, min_rating: float, max_results: int) -> List[Dict[str, Any]]:
    """
    Fallback function that returns mock Amazon products when SerpAPI is not available.

    Args:
        keywords: Search keywords
        product_category: Product category
        price_min: Minimum price filter
        price_max: Maximum price filter
        min_rating: Minimum rating filter
        max_results: Maximum number of results

    Returns:
        List of mock products
    """
    # Simulated hot products data
    mock_products = [
        {
            "title": f"Premium {keywords} - Best Seller",
            "price": 29.99,
            "rating": 4.5,
            "reviews_count": 2847,
            "image_url": "https://example.com/product1.jpg",
            "product_url": "https://amazon.com/product1",
            "category": product_category,
            "asin": "B0MOCK001",
            "keywords": keywords.split(),
            "monthly_sales": 15000,
            "trend": "increasing",
            "competition_level": "medium",
            "bought_last_month": "15K+ bought in past month",
            "prime": True,
            "sponsored": False
        },
        {
            "title": f"Professional {keywords} Kit",
            "price": 45.99,
            "rating": 4.7,
            "reviews_count": 1923,
            "image_url": "https://example.com/product2.jpg",
            "product_url": "https://amazon.com/product2",
            "category": product_category,
            "asin": "B0MOCK002",
            "keywords": keywords.split(),
            "monthly_sales": 12000,
            "trend": "stable",
            "competition_level": "low",
            "bought_last_month": "12K+ bought in past month",
            "prime": True,
            "sponsored": True
        },
        {
            "title": f"Deluxe {keywords} Set",
            "price": 19.99,
            "rating": 4.3,
            "reviews_count": 3456,
            "image_url": "https://example.com/product3.jpg",
            "product_url": "https://amazon.com/product3",
            "category": product_category,
            "asin": "B0MOCK003",
            "keywords": keywords.split(),
            "monthly_sales": 8500,
            "trend": "increasing",
            "competition_level": "high",
            "bought_last_month": "8.5K+ bought in past month",
            "prime": False,
            "sponsored": False
        }
    ]

    # Filter by price range and rating
    filtered_products = [
        product for product in mock_products
        if (price_min <= product["price"] <= price_max and
            product["rating"] >= min_rating)
    ]

    return filtered_products[:max_results]


def _convert_image_url(image_url: str, api_token: str) -> Optional[str]:
    """
    Convert Amazon image URL to 1688-compatible format using TMAPI image conversion endpoint.

    Args:
        image_url: Original Amazon image URL
        api_token: TMAPI API token

    Returns:
        Converted image URL or None if conversion fails
    """
    if not image_url or not api_token:
        return None

    try:
        # TMAPI image conversion endpoint
        conversion_url = "http://api.tmapi.top/1688/tools/image/convert_url"

        # Prepare conversion request parameters (API token in query string)
        querystring = {
            "apiToken": api_token
        }

        # Prepare JSON payload (image URL in request body)
        payload = {
            "url": image_url
        }

        print(f"🔄 正在转换图片URL：{image_url[:60]}...")

        # Execute conversion request using POST with JSON payload
        response = requests.post(conversion_url, json=payload, params=querystring, timeout=30)
        response.raise_for_status()

        # Parse conversion response
        conversion_data = response.json()

        # Check for conversion errors
        if conversion_data.get("code") != 200:
            error_msg = conversion_data.get("msg", "未知转换错误")
            print(f"⚠️  图片URL转换失败：{error_msg}")
            return None

        # Extract converted URL from data.image_url field
        data = conversion_data.get("data", {})
        converted_url = data.get("image_url")
        if not converted_url:
            print("⚠️  转换响应中未找到转换后的URL")
            return None

        print(f"✅ 图片URL转换成功：{converted_url[:60]}...")
        return converted_url

    except requests.exceptions.RequestException as e:
        print(f"⚠️  图片URL转换请求失败：{e}")
        return None
    except Exception as e:
        print(f"⚠️  图片URL转换失败：{e}")
        return None


def _export_1688_api_response(response_data: Dict[str, Any], search_query: str, image_url: str, export_dir: str = None) -> str:
    """
    Export 1688 API response to a JSON file for debugging purposes.

    Args:
        response_data: The raw 1688 API response
        search_query: The search query used
        image_url: The image URL used for search
        export_dir: Directory to save the export files (uses env var if None)

    Returns:
        Path to the exported file
    """
    # Check if export is enabled
    if not os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        return ""

    try:
        # Use environment variable for export directory if not specified
        if export_dir is None:
            base_export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
            export_dir = _get_current_task_directory(base_export_dir)

        # Generate filename with timestamp prefix
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        safe_query = re.sub(r'[^\w\s-]', '', search_query).strip()
        safe_query = re.sub(r'[-\s]+', '_', safe_query)
        filename = f"{timestamp}_1688_api_response_{safe_query}.json"
        filepath = os.path.join(export_dir, filename)

        # Prepare export data
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "search_query": search_query,
            "image_url": image_url,
            "raw_response": response_data,
            "metadata": {
                "total_results": response_data.get("data", {}).get("total_count", 0) if response_data.get("code") == 200 else 0,
                "has_error": response_data.get("code", 500) != 200,
                "page": response_data.get("data", {}).get("page", 1),
                "page_size": response_data.get("data", {}).get("page_size", 0),
                "api_status": response_data.get("msg", "unknown")
            }
        }

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📁 1688 API 响应已导出到：{filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️  导出 1688 API 响应失败：{e}")
        return ""


def _transform_1688_result(item: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Transform a 1688 API result into our supplier format.

    Args:
        item: Single item from 1688 API response

    Returns:
        Transformed supplier dictionary or None if invalid
    """
    try:
        # Extract basic information
        title = item.get("title", "")
        if not title:
            return None

        # Extract price information
        price_info = item.get("price_info", {})
        wholesale_price = float(price_info.get("wholesale_price", item.get("price", "0")))

        # Extract shop information
        shop_info = item.get("shop_info", {})
        supplier_name = shop_info.get("login_id", "Unknown Supplier")
        company_name = shop_info.get("company_name", "Unknown Company")

        # Extract location
        location_list = shop_info.get("location", [])
        location = ", ".join(location_list) if location_list else "Unknown Location"

        # Extract minimum order quantity
        quantity_begin = item.get("quantity_begin", 1)
        quantity_prices = item.get("quantity_prices", [])
        min_order_qty = quantity_begin
        if quantity_prices and len(quantity_prices) > 0:
            min_order_qty = int(quantity_prices[0].get("begin_num", quantity_begin))

        # Calculate supplier rating based on available metrics
        goods_score = item.get("goods_score", 3)
        tp_year = shop_info.get("tp_year", 1)
        is_factory = shop_info.get("is_factory", False)

        # Create a composite rating (1-5 scale)
        supplier_rating = min(5.0, max(1.0, (goods_score + (tp_year * 0.1) + (1 if is_factory else 0)) / 2))

        # Extract delivery information
        delivery_info = item.get("delivery_info", {})
        area_from = delivery_info.get("area_from", [])
        delivery_location = ", ".join(area_from) if area_from else location

        # Estimate shipping cost based on location and price
        shipping_cost = _estimate_shipping_cost(wholesale_price, delivery_location)

        # Estimate delivery time
        delivery_time = _estimate_delivery_time(delivery_location)

        # Extract certifications (inferred from shop type and factory status)
        certifications = []
        if is_factory:
            certifications.append("Factory Direct")
        if shop_info.get("tp_member", False):
            certifications.append("TP Member")
        if shop_info.get("factory_inspection", False):
            certifications.append("Factory Inspected")

        # Build supplier dictionary
        supplier = {
            "supplier_name": supplier_name,
            "company_name": company_name,
            "wholesale_price": wholesale_price,
            "minimum_order_quantity": min_order_qty,
            "supplier_rating": round(supplier_rating, 1),
            "years_in_business": tp_year,
            "location": location,
            "contact_info": {
                "shop_url": shop_info.get("shop_url", ""),
                "member_id": shop_info.get("member_id", ""),
                "login_id": supplier_name
            },
            "product_images": [item.get("img", "")],
            "shipping_cost": shipping_cost,
            "delivery_time": delivery_time,
            "certifications": certifications,
            "trade_assurance": shop_info.get("tp_member", False),
            "response_rate": "N/A",  # Not available in API response
            "product_url": item.get("product_url", ""),
            "item_id": str(item.get("item_id", "")),
            "biz_type": shop_info.get("biz_type", "Unknown"),
            "is_factory": is_factory,
            "goods_score": goods_score
        }

        return supplier

    except Exception as e:
        print(f"⚠️  转换 1688 结果时出错：{e}")
        return None


def _estimate_shipping_cost(wholesale_price: float, location: str) -> float:
    """
    Estimate shipping cost based on wholesale price and location.

    Args:
        wholesale_price: Wholesale price of the product
        location: Supplier location

    Returns:
        Estimated shipping cost per unit
    """
    # Base shipping cost
    base_cost = 2.0

    # Adjust based on price (higher value items have higher shipping)
    price_factor = min(wholesale_price * 0.1, 5.0)

    # Adjust based on location (some regions have higher shipping costs)
    location_factor = 1.0
    if "广东" in location or "浙江" in location:  # Major manufacturing regions
        location_factor = 0.8
    elif "新疆" in location or "西藏" in location:  # Remote regions
        location_factor = 1.5

    return round(base_cost + price_factor * location_factor, 2)


def _estimate_delivery_time(location: str) -> str:
    """
    Estimate delivery time based on supplier location.

    Args:
        location: Supplier location

    Returns:
        Estimated delivery time string
    """
    # Default delivery time
    if "广东" in location or "浙江" in location or "江苏" in location:
        return "7-12 days"
    elif "上海" in location or "北京" in location:
        return "5-10 days"
    elif "新疆" in location or "西藏" in location or "内蒙古" in location:
        return "15-25 days"
    else:
        return "10-15 days"





# Tool 2: 1688 Supplier Platform API Integration
@tool
def find_1688_suppliers(
    image_url: str,
    max_suppliers: int = 5
) -> List[Dict[str, Any]]:
    """
    Find suppliers on 1688 platform using TMAPI image search.

    This function first converts Amazon image URLs to 1688-compatible format using
    TMAPI image conversion endpoint, then performs the supplier search.

    Args:
        image_url: Amazon image URL for product search (will be converted automatically)
        max_suppliers: Maximum number of suppliers to return

    Returns:
        List of suppliers with wholesale prices, MOQ, ratings, and contact information
    """

    # Initialize task directory for this execution (only if not already initialized)
    if os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        # Check if task directory already exists, if not initialize it
        global _current_task_directory
        if _current_task_directory is None:
            base_export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
            task_dir = _initialize_task_directory(base_export_dir)
            print(f"📁 任务目录已初始化：{task_dir}")

    print(f"🏭 正在搜索 1688 供应商")
    print(f"   图片URL：{image_url[:60]}...")

    # Check if mock mode is enabled
    use_mock_data = os.getenv("USE_MOCK_DATA", "false").lower() == "true"
    if use_mock_data:
        print("🎭 使用模拟数据模式")
        return _get_mock_1688_suppliers(image_url, max_suppliers)

    # Get API token from environment
    api_token = os.getenv("TMAPI_1688_TOKEN")
    if not api_token:
        print("⚠️  未设置 TMAPI_1688_TOKEN 环境变量，无法进行真实 API 调用")
        return []

    try:
        if not image_url:
            print("⚠️  没有可用的产品图片进行搜索，无法进行真实 API 调用")
            return []

        # Step 1: Convert Amazon image URL to 1688-compatible format
        converted_image_url = _convert_image_url(image_url, api_token)
        if not converted_image_url:
            print("❌ 图片URL转换失败，无法进行1688搜索")
            print("   💡 提示：请检查图片URL格式或网络连接")
            return []

        # Step 2: Use converted URL for 1688 search
        # Prepare TMAPI request parameters
        url = "http://api.tmapi.top/1688/search/image"
        querystring = {
            "apiToken": api_token,
            "img_url": converted_image_url,  # Use converted URL instead of original
            "page": 1,
            "page_size": 20,  # Get more results to filter
            "sort": "default"
        }

        print(f"🔍 正在使用转换后的图片搜索 1688：{converted_image_url[:60]}...")

        # Execute API request
        response = requests.get(url, params=querystring, timeout=30)
        response.raise_for_status()

        # Parse response
        api_data = response.json()

        # Export API response for debugging
        export_path = _export_1688_api_response(api_data, "image_search", converted_image_url)

        # Check for API errors
        if api_data.get("code") != 200:
            error_msg = api_data.get("msg", "未知错误")
            print(f"❌ 1688 API 错误：{error_msg}")
            if export_path:
                print(f"   错误详情已保存到：{export_path}")
            return []

        # Extract items from response
        data = api_data.get("data", {})
        items = data.get("items", [])

        if not items:
            print("⚠️  1688 API 响应中未找到供应商")
            return []

        print(f"📦 从 1688 API 找到 {len(items)} 个潜在供应商")

        # Transform API results to our format
        transformed_suppliers = []
        for item in items:
            try:
                supplier = _transform_1688_result(item)
                if supplier:
                    transformed_suppliers.append(supplier)
                    if len(transformed_suppliers) >= max_suppliers:
                        break
            except Exception as e:
                print(f"⚠️  处理 1688 供应商时出错：{e}")
                continue

        # Sort by rating and price
        transformed_suppliers.sort(key=lambda x: (-x["supplier_rating"], x["wholesale_price"]))

        print(f"✅ 找到 {len(transformed_suppliers)} 个供应商")

        # Export processed results for debugging
        if transformed_suppliers:
            _export_1688_processed_results(transformed_suppliers, "image_search", converted_image_url)

        return transformed_suppliers

    except requests.exceptions.RequestException as e:
        print(f"❌ 1688 API 请求失败：{e}")
        return []
    except Exception as e:
        print(f"❌ 1688 API 搜索失败：{e}")
        return []


def _get_product_image_url(product_title: str) -> Optional[str]:
    """
    Get a product image URL for 1688 search. This function serves as a fallback
    when no image URL is provided directly from Amazon search results.

    Args:
        product_title: Product title to search for

    Returns:
        Image URL or None if not available
    """
    # This function is now primarily a fallback mechanism
    # The main workflow should pass image URLs directly from Amazon search results
    # to the find_1688_suppliers function via the product_image_url parameter

    print(f"⚠️  未提供图片 URL：{product_title}")
    print("   💡 提示：请先使用亚马逊搜索结果，然后将 image_url 传递给 find_1688_suppliers")
    print("   📝 示例：find_1688_suppliers(..., product_image_url=amazon_product['image_url'])")

    # In a production environment, you could implement additional fallback mechanisms:
    # - Search for product images using Google Images API
    # - Use a product database with known image URLs
    # - Extract images from e-commerce sites
    # - Use AI-generated product images based on descriptions

    return None  # This will trigger fallback to mock data


def _export_1688_processed_results(suppliers: List[Dict[str, Any]], search_query: str, image_url: str, export_dir: str = None) -> str:
    """
    Export processed 1688 supplier results to a JSON file for debugging purposes.

    Args:
        suppliers: The processed supplier list
        search_query: The search query used
        image_url: The image URL used for search
        export_dir: Directory to save the export files (uses env var if None)

    Returns:
        Path to the exported file
    """
    # Check if export is enabled
    if not os.getenv("EXPORT_API_RESPONSES", "false").lower() == "true":
        return ""

    try:
        # Use environment variable for export directory if not specified
        if export_dir is None:
            base_export_dir = os.getenv("DEBUG_EXPORT_DIR", "debug_exports")
            export_dir = _get_current_task_directory(base_export_dir)

        # Generate filename with timestamp prefix
        timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        safe_query = re.sub(r'[^\w\s-]', '', search_query).strip()
        safe_query = re.sub(r'[-\s]+', '_', safe_query)
        filename = f"{timestamp}_1688_processed_results_{safe_query}.json"
        filepath = os.path.join(export_dir, filename)

        # Prepare export data
        export_data = {
            "timestamp": datetime.now().isoformat(),
            "search_query": search_query,
            "image_url": image_url,
            "total_suppliers": len(suppliers),
            "processed_suppliers": suppliers,
            "summary": {
                "price_range": {
                    "min": min([s.get("wholesale_price", 0) for s in suppliers]) if suppliers else 0,
                    "max": max([s.get("wholesale_price", 0) for s in suppliers]) if suppliers else 0,
                    "avg": sum([s.get("wholesale_price", 0) for s in suppliers]) / len(suppliers) if suppliers else 0
                },
                "rating_range": {
                    "min": min([s.get("supplier_rating", 0) for s in suppliers]) if suppliers else 0,
                    "max": max([s.get("supplier_rating", 0) for s in suppliers]) if suppliers else 0,
                    "avg": sum([s.get("supplier_rating", 0) for s in suppliers]) / len(suppliers) if suppliers else 0
                },
                "moq_range": {
                    "min": min([s.get("minimum_order_quantity", 0) for s in suppliers]) if suppliers else 0,
                    "max": max([s.get("minimum_order_quantity", 0) for s in suppliers]) if suppliers else 0,
                    "avg": sum([s.get("minimum_order_quantity", 0) for s in suppliers]) / len(suppliers) if suppliers else 0
                },
                "factory_suppliers": len([s for s in suppliers if s.get("is_factory", False)]),
                "trade_assurance_suppliers": len([s for s in suppliers if s.get("trade_assurance", False)])
            }
        }

        # Write to file
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)

        print(f"📊 1688 处理结果已导出到：{filepath}")
        return filepath

    except Exception as e:
        print(f"⚠️  导出 1688 处理结果失败：{e}")
        return ""


def _get_mock_1688_api_response(search_type: str, max_suppliers: int) -> Dict[str, Any]:
    """
    Generate mock 1688 API response that matches the real API structure.

    Args:
        search_type: Type of search being performed
        max_suppliers: Maximum number of suppliers to return

    Returns:
        Mock API response in the exact same structure as real 1688 API
    """
    # Use realistic wholesale prices for mock data
    price1 = 5.50
    price2 = 6.25
    price3 = 4.80

    # Mock API response matching real structure exactly
    mock_response = {
        "code": 200,
        "msg": "success",
        "data": {
            "page": 1,
            "page_size": max_suppliers,
            "total_count": 1250,
            "sort": "default",
            "price_start": "",
            "price_end": "",
            "items": [
                {
                    "item_id": ************,
                    "product_url": "https://detail.1688.com/offer/************.html",
                    "title": "高品质产品工厂直销批发定制OEM源头供应商",
                    "img": "https://cbu01.alicdn.com/img/ibank/O1CN01DUXYg51qmS6hp1Ng1_!!*************-0-cib.jpg",
                    "category_path": ["1034783", "*********", "97"],
                    "price": str(price1),
                    "price_info": {
                        "drop_ship_price": str(price1),
                        "wholesale_price": str(price1),
                        "origin_price": str(round(price1 * 1.2, 2))
                    },
                    "quantity_begin": 500,
                    "quantity_prices": [
                        {
                            "begin_num": "500",
                            "end_num": "",
                            "price": str(price1)
                        }
                    ],
                    "sale_info": {
                        "gmv_30days": 15000,
                        "gmv_30days_cb": 12000,
                        "sale_quantity": 2500,
                        "orders_count": 45
                    },
                    "type": "normal",
                    "delivery_info": {
                        "area_from": ["广东", "深圳市"],
                        "weight": 0.5,
                        "suttle_weight": 0.4,
                        "free_postage": False
                    },
                    "item_repurchase_rate": "85%",
                    "goods_score": 5,
                    "image_dsm_score": 8,
                    "primary_rank_score": 9,
                    "super_new_product": False,
                    "shop_info": {
                        "login_id": "金龙制造",
                        "member_id": "b2b-*************a4640",
                        "company_name": "深圳市金龙电子科技有限公司",
                        "shop_url": "https://shop1v5n4924b2307.1688.com",
                        "biz_type": "生产加工",
                        "is_factory": True,
                        "location": ["广东", "深圳市"],
                        "service_tags": ["金牌供应商", "实地认证"],
                        "tp_member": True,
                        "tp_year": 12,
                        "factory_inspection": True,
                        "shop_repurchase_rate": "98%",
                        "super_factory": True
                    }
                },
                {
                    "item_id": ************,
                    "product_url": "https://detail.1688.com/offer/************.html",
                    "title": "专业产品贸易公司批发零售一件代发",
                    "img": "https://cbu01.alicdn.com/img/ibank/O1CN01m2N7vT27PPfpCayub_!!*************-0-cib.jpg",
                    "category_path": ["1034783", "*********", "97"],
                    "price": str(price2),
                    "price_info": {
                        "drop_ship_price": str(price2),
                        "wholesale_price": str(price2),
                        "origin_price": str(round(price2 * 1.15, 2))
                    },
                    "quantity_begin": 300,
                    "quantity_prices": [
                        {
                            "begin_num": "300",
                            "end_num": "",
                            "price": str(price2)
                        }
                    ],
                    "sale_info": {
                        "gmv_30days": 8500,
                        "gmv_30days_cb": 7200,
                        "sale_quantity": 1800,
                        "orders_count": 32
                    },
                    "type": "normal",
                    "delivery_info": {
                        "area_from": ["浙江", "义乌市"],
                        "weight": 0.3,
                        "suttle_weight": 0.25,
                        "free_postage": False
                    },
                    "item_repurchase_rate": "72%",
                    "goods_score": 4,
                    "image_dsm_score": 7,
                    "primary_rank_score": 6,
                    "super_new_product": False,
                    "shop_info": {
                        "login_id": "日升贸易",
                        "member_id": "b2b-*************7d179",
                        "company_name": "义乌市日升贸易有限公司",
                        "shop_url": "https://shop7834174v386s6.1688.com",
                        "biz_type": "贸易型",
                        "is_factory": False,
                        "location": ["浙江", "义乌市"],
                        "service_tags": ["诚信通"],
                        "tp_member": True,
                        "tp_year": 8,
                        "factory_inspection": False,
                        "shop_repurchase_rate": "95%",
                        "super_factory": False
                    }
                },
                {
                    "item_id": ************,
                    "product_url": "https://detail.1688.com/offer/************.html",
                    "title": "OEM代加工产品工厂直供品质保证",
                    "img": "https://cbu01.alicdn.com/img/ibank/O1CN01zayetG1sUFisQkMwW_!!**********-0-cib.jpg",
                    "category_path": ["1043499", "1043498", "97"],
                    "price": str(price3),
                    "price_info": {
                        "drop_ship_price": str(price3),
                        "wholesale_price": str(price3),
                        "origin_price": str(round(price3 * 1.25, 2))
                    },
                    "quantity_begin": 1000,
                    "quantity_prices": [
                        {
                            "begin_num": "1000",
                            "end_num": "",
                            "price": str(price3)
                        }
                    ],
                    "sale_info": {
                        "gmv_30days": 22000,
                        "gmv_30days_cb": 18500,
                        "sale_quantity": 3200,
                        "orders_count": 58
                    },
                    "type": "normal",
                    "delivery_info": {
                        "area_from": ["广东", "东莞市"],
                        "weight": 0.4,
                        "suttle_weight": 0.35,
                        "free_postage": False
                    },
                    "item_repurchase_rate": "91%",
                    "goods_score": 5,
                    "image_dsm_score": 9,
                    "primary_rank_score": 8,
                    "super_new_product": False,
                    "shop_info": {
                        "login_id": "凤凰制造",
                        "member_id": "b2b-**********da0bb",
                        "company_name": "东莞市凤凰电子制造有限公司",
                        "shop_url": "https://shop1469206152486.1688.com",
                        "biz_type": "生产加工",
                        "is_factory": True,
                        "location": ["广东", "东莞市"],
                        "service_tags": ["金牌供应商", "实地认证", "深度验厂"],
                        "tp_member": True,
                        "tp_year": 15,
                        "factory_inspection": True,
                        "shop_repurchase_rate": "99%",
                        "super_factory": True
                    }
                }
            ][:max_suppliers]  # Limit to requested number of suppliers
        }
    }

    return mock_response


def _get_mock_1688_suppliers(image_url: str, max_suppliers: int) -> List[Dict[str, Any]]:
    """
    Fallback function that returns mock 1688 suppliers when API is not available.
    Uses the same data transformation pipeline as real API responses.

    Args:
        image_url: Image URL for search
        max_suppliers: Maximum number of suppliers

    Returns:
        List of mock suppliers in the same format as real API processing
    """
    print("📋 使用模拟 1688 供应商数据（匹配真实 API 结构）")

    # Generate mock API response in real format
    mock_api_response = _get_mock_1688_api_response("image_search", max_suppliers)

    # Process the mock response using the same transformation pipeline as real API
    items = mock_api_response.get("data", {}).get("items", [])

    # Transform API results to our format using the same function as real API
    transformed_suppliers = []
    for item in items:
        try:
            supplier = _transform_1688_result(item)
            if supplier:
                transformed_suppliers.append(supplier)
                if len(transformed_suppliers) >= max_suppliers:
                    break
        except Exception as e:
            print(f"⚠️  处理模拟供应商时出错：{e}")
            continue

    # Sort by rating and price (same as real API)
    transformed_suppliers.sort(key=lambda x: (-x["supplier_rating"], x["wholesale_price"]))

    return transformed_suppliers[:max_suppliers]

@app.entrypoint
async def create_ecommerce_sourcing_agent(payload):
    """
    Create and configure the e-commerce product sourcing agent.
    
    Returns:
        Configured Strands Agent instance
    """
    
    # System prompt for the agent
    system_prompt = """您是"产品采购专家"，一个智能的电商采购助手，帮助用户找到有利可图的产品和可靠的供应商。

您的主要功能：
1. 根据用户需求搜索亚马逊热销产品
2. 在1688平台上找到具有竞争力批发价格的对应供应商
3. 提供包括利润率、市场趋势和供应商可靠性在内的综合分析

交互指南：
- 始终专业地问候用户并介绍自己为产品采购专家
- 如果用户需求不明确，请提出澄清问题
- 提供产品和供应商的详细分析
- 计算潜在利润率和投资回报率
- 突出重要因素，如最小订购量、运费和交货时间
- 警告潜在风险或挑战
- 始终提供可行的建议

处理请求时：
1. 首先了解用户的产品需求（类别、价格范围、目标市场）
2. 搜索符合条件的亚马逊热销产品
3. 对于每个有前景的产品，使用亚马逊产品的图片URL在1688上找到合适的供应商
4. 分析数据并提供综合建议
5. 以清晰、有组织的格式呈现结果

重要提示：调用 find_1688_suppliers 时，始终包含来自亚马逊搜索结果的 product_image_url 参数，以启用1688上的真实图像搜索。从亚马逊产品数据中提取 image_url 字段并将其传递给1688搜索功能。

工作流程示例：
1. 调用 search_amazon_hot_products 获取包含 image_url 的产品数据
2. 对于每个产品，调用 find_1688_suppliers 并将 product_image_url 设置为亚马逊产品的 image_url
3. 这样可以启用真实的1688 API搜索，而不是回退到模拟数据

重要考虑因素：
- 考虑所有成本：产品成本、运费、亚马逊费用、营销费用
- 考虑市场竞争和趋势
- 评估供应商可靠性和认证
- 建议最佳订购量和定价策略
- 在可用时始终使用亚马逊产品图片进行1688搜索

请在 <recommendation></recommendation> 标签内提供您的最终建议。最终建议里面也提供供应商的商品url，这样用户可以点击进去查看商品信息。
请仅用中文回复，不要使用英文
"""

    # Configure the model
    model = BedrockModel(
        #model_id="us.anthropic.claude-sonnet-4-20250514-v1:0",
        model_id="us.anthropic.claude-3-7-sonnet-20250219-v1:0",
        additional_request_fields={
            "thinking": {
                "type": "disabled",
            }
        },
    )
    
    # Create the agent with custom tools
    agent = Agent(
        model=model,
        system_prompt=system_prompt,
        tools=[search_amazon_hot_products, find_1688_suppliers],
        name="ecommerce-sourcing-agent"
    )
    
    user_input = payload.get("prompt")

    stream = agent.stream_async(user_input)
    async for event in stream:
        print(event)
        yield (event)



if __name__ == "__main__":
    task_id = app.add_async_task("strands_agent_ecommerce_async")
    app.run()
    app.complete_async_task(task_id)