{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://iamcredentials.googleapis.com/", "batchPath": "batch", "canonicalName": "IAM Credentials", "description": "Creates short-lived credentials for impersonating IAM service accounts. Disabling this API also disables the IAM API (iam.googleapis.com). However, enabling this API doesn't enable the IAM API. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/iam/docs/creating-short-lived-service-account-credentials", "endpoints": [{"description": "Regional Endpoint", "endpointUrl": "https://iamcredentials.asia-east1.rep.googleapis.com/", "location": "asia-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://iamcredentials.europe-west1.rep.googleapis.com/", "location": "europe-west1"}, {"description": "Regional Endpoint", "endpointUrl": "https://iamcredentials.us-central1.rep.googleapis.com/", "location": "us-central1"}, {"description": "Regional Endpoint", "endpointUrl": "https://iamcredentials.us-east1.rep.googleapis.com/", "location": "us-east1"}, {"description": "Regional Endpoint", "endpointUrl": "https://iamcredentials.us-east7.rep.googleapis.com/", "location": "us-east7"}], "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "iamcredentials:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://iamcredentials.mtls.googleapis.com/", "name": "iamcredentials", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"locations": {"resources": {"workforcePools": {"methods": {"getAllowedLocations": {"description": "Returns the trust boundary info for a given workforce pool.", "flatPath": "v1/locations/{locationsId}/workforcePools/{workforcePoolsId}/allowedLocations", "httpMethod": "GET", "id": "iamcredentials.locations.workforcePools.getAllowedLocations", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of workforce pool.", "location": "path", "pattern": "^locations/[^/]+/workforcePools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}/allowedLocations", "response": {"$ref": "WorkforcePoolAllowedLocations"}}}}}}, "projects": {"resources": {"locations": {"resources": {"workloadIdentityPools": {"methods": {"getAllowedLocations": {"description": "Returns the trust boundary info for a given workload identity pool.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/workloadIdentityPools/{workloadIdentityPoolsId}/allowedLocations", "httpMethod": "GET", "id": "iamcredentials.projects.locations.workloadIdentityPools.getAllowedLocations", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of workload identity pool.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/workloadIdentityPools/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}/allowedLocations", "response": {"$ref": "WorkloadIdentityPoolAllowedLocations"}}}}}}, "serviceAccounts": {"methods": {"generateAccessToken": {"description": "Generates an OAuth 2.0 access token for a service account.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:generateAccessToken", "httpMethod": "POST", "id": "iamcredentials.projects.serviceAccounts.generateAccessToken", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account for which the credentials are requested, in the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard character is required; replacing it with a project ID is invalid.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:generateAccessToken", "request": {"$ref": "GenerateAccessTokenRequest"}, "response": {"$ref": "GenerateAccessTokenResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "generateIdToken": {"description": "Generates an OpenID Connect ID token for a service account.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:generateIdToken", "httpMethod": "POST", "id": "iamcredentials.projects.serviceAccounts.generateIdToken", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account for which the credentials are requested, in the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard character is required; replacing it with a project ID is invalid.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:generateIdToken", "request": {"$ref": "GenerateIdTokenRequest"}, "response": {"$ref": "GenerateIdTokenResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getAllowedLocations": {"description": "Returns the trust boundary info for a given service account.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}/allowedLocations", "httpMethod": "GET", "id": "iamcredentials.projects.serviceAccounts.getAllowedLocations", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Resource name of service account.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}/allowedLocations", "response": {"$ref": "ServiceAccountAllowedLocations"}}, "signBlob": {"description": "Signs a blob using a service account's system-managed private key.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:signBlob", "httpMethod": "POST", "id": "iamcredentials.projects.serviceAccounts.signBlob", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account for which the credentials are requested, in the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard character is required; replacing it with a project ID is invalid.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:signBlob", "request": {"$ref": "SignBlobRequest"}, "response": {"$ref": "SignBlobResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "signJwt": {"description": "Signs a JWT using a service account's system-managed private key.", "flatPath": "v1/projects/{projectsId}/serviceAccounts/{serviceAccountsId}:signJwt", "httpMethod": "POST", "id": "iamcredentials.projects.serviceAccounts.signJwt", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the service account for which the credentials are requested, in the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard character is required; replacing it with a project ID is invalid.", "location": "path", "pattern": "^projects/[^/]+/serviceAccounts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:signJwt", "request": {"$ref": "SignJwtRequest"}, "response": {"$ref": "SignJwtResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}, "revision": "********", "rootUrl": "https://iamcredentials.googleapis.com/", "schemas": {"GenerateAccessTokenRequest": {"id": "GenerateAccessTokenRequest", "properties": {"delegates": {"description": "The sequence of service accounts in a delegation chain. This field is required for [delegated requests](https://cloud.google.com/iam/help/credentials/delegated-request). For [direct requests](https://cloud.google.com/iam/help/credentials/direct-request), which are more common, do not specify this field. Each service account must be granted the `roles/iam.serviceAccountTokenCreator` role on its next service account in the chain. The last service account in the chain must be granted the `roles/iam.serviceAccountTokenCreator` role on the service account that is specified in the `name` field of the request. The delegates must have the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard character is required; replacing it with a project ID is invalid.", "items": {"type": "string"}, "type": "array"}, "lifetime": {"description": "The desired lifetime duration of the access token in seconds. By default, the maximum allowed value is 1 hour. To set a lifetime of up to 12 hours, you can add the service account as an allowed value in an Organization Policy that enforces the `constraints/iam.allowServiceAccountCredentialLifetimeExtension` constraint. See detailed instructions at https://cloud.google.com/iam/help/credentials/lifetime If a value is not specified, the token's lifetime will be set to a default value of 1 hour.", "format": "google-duration", "type": "string"}, "scope": {"description": "Required. Code to identify the scopes to be included in the OAuth 2.0 access token. See https://developers.google.com/identity/protocols/googlescopes for more information. At least one value required.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GenerateAccessTokenResponse": {"id": "GenerateAccessTokenResponse", "properties": {"accessToken": {"description": "The OAuth 2.0 access token.", "type": "string"}, "expireTime": {"description": "Token expiration time. The expiration time is always set.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GenerateIdTokenRequest": {"id": "GenerateIdTokenRequest", "properties": {"audience": {"description": "Required. The audience for the token, such as the API or account that this token grants access to.", "type": "string"}, "delegates": {"description": "The sequence of service accounts in a delegation chain. Each service account must be granted the `roles/iam.serviceAccountTokenCreator` role on its next service account in the chain. The last service account in the chain must be granted the `roles/iam.serviceAccountTokenCreator` role on the service account that is specified in the `name` field of the request. The delegates must have the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard character is required; replacing it with a project ID is invalid.", "items": {"type": "string"}, "type": "array"}, "includeEmail": {"description": "Include the service account email in the token. If set to `true`, the token will contain `email` and `email_verified` claims.", "type": "boolean"}, "organizationNumberIncluded": {"description": "Include the organization number of the service account in the token. If set to `true`, the token will contain a `google.organization_number` claim. The value of the claim will be `null` if the service account isn't associated with an organization.", "type": "boolean"}}, "type": "object"}, "GenerateIdTokenResponse": {"id": "GenerateIdTokenResponse", "properties": {"token": {"description": "The OpenId Connect ID token. The token is a JSON Web Token (JWT) that contains a payload with claims. See the [JSON Web Token spec](https://tools.ietf.org/html/rfc7519) for more information. Here is an example of a decoded JWT payload: ``` { \"iss\": \"https://accounts.google.com\", \"iat\": **********, \"exp\": **********, \"aud\": \"https://www.example.com\", \"sub\": \"107517467455664443765\", \"azp\": \"107517467455664443765\", \"email\": \"<EMAIL>\", \"email_verified\": true, \"google\": { \"organization_number\": 123456 } } ```", "type": "string"}}, "type": "object"}, "ServiceAccountAllowedLocations": {"description": "Represents a list of allowed locations for given service account.", "id": "ServiceAccountAllowedLocations", "properties": {"encodedLocations": {"description": "Output only. The hex encoded bitmap of the trust boundary locations", "readOnly": true, "type": "string"}, "locations": {"description": "Output only. The human readable trust boundary locations. For example, [\"us-central1\", \"europe-west1\"]", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "SignBlobRequest": {"id": "SignBlobRequest", "properties": {"delegates": {"description": "The sequence of service accounts in a delegation chain. Each service account must be granted the `roles/iam.serviceAccountTokenCreator` role on its next service account in the chain. The last service account in the chain must be granted the `roles/iam.serviceAccountTokenCreator` role on the service account that is specified in the `name` field of the request. The delegates must have the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard character is required; replacing it with a project ID is invalid.", "items": {"type": "string"}, "type": "array"}, "payload": {"description": "Required. The bytes to sign.", "format": "byte", "type": "string"}}, "type": "object"}, "SignBlobResponse": {"id": "SignBlobResponse", "properties": {"keyId": {"description": "The ID of the key used to sign the blob. The key used for signing will remain valid for at least 12 hours after the blob is signed. To verify the signature, you can retrieve the public key in several formats from the following endpoints: - RSA public key wrapped in an X.509 v3 certificate: `https://www.googleapis.com/service_accounts/v1/metadata/x509/{ACCOUNT_EMAIL}` - Raw key in JSON format: `https://www.googleapis.com/service_accounts/v1/metadata/raw/{ACCOUNT_EMAIL}` - JSON Web Key (JWK): `https://www.googleapis.com/service_accounts/v1/metadata/jwk/{ACCOUNT_EMAIL}`", "type": "string"}, "signedBlob": {"description": "The signature for the blob. Does not include the original blob. After the key pair referenced by the `key_id` response field expires, Google no longer exposes the public key that can be used to verify the blob. As a result, the receiver can no longer verify the signature.", "format": "byte", "type": "string"}}, "type": "object"}, "SignJwtRequest": {"id": "SignJwtRequest", "properties": {"delegates": {"description": "The sequence of service accounts in a delegation chain. Each service account must be granted the `roles/iam.serviceAccountTokenCreator` role on its next service account in the chain. The last service account in the chain must be granted the `roles/iam.serviceAccountTokenCreator` role on the service account that is specified in the `name` field of the request. The delegates must have the following format: `projects/-/serviceAccounts/{ACCOUNT_EMAIL_OR_UNIQUEID}`. The `-` wildcard character is required; replacing it with a project ID is invalid.", "items": {"type": "string"}, "type": "array"}, "payload": {"description": "Required. The JWT payload to sign. Must be a serialized JSON object that contains a JWT Claims Set. For example: `{\"sub\": \"<EMAIL>\", \"iat\": 313435}` If the JWT Claims Set contains an expiration time (`exp`) claim, it must be an integer timestamp that is not in the past and no more than 12 hours in the future.", "type": "string"}}, "type": "object"}, "SignJwtResponse": {"id": "SignJwtResponse", "properties": {"keyId": {"description": "The ID of the key used to sign the JWT. The key used for signing will remain valid for at least 12 hours after the JWT is signed. To verify the signature, you can retrieve the public key in several formats from the following endpoints: - RSA public key wrapped in an X.509 v3 certificate: `https://www.googleapis.com/service_accounts/v1/metadata/x509/{ACCOUNT_EMAIL}` - Raw key in JSON format: `https://www.googleapis.com/service_accounts/v1/metadata/raw/{ACCOUNT_EMAIL}` - JSON Web Key (JWK): `https://www.googleapis.com/service_accounts/v1/metadata/jwk/{ACCOUNT_EMAIL}`", "type": "string"}, "signedJwt": {"description": "The signed JWT. Contains the automatically generated header; the client-supplied payload; and the signature, which is generated using the key referenced by the `kid` field in the header. After the key pair referenced by the `key_id` response field expires, Google no longer exposes the public key that can be used to verify the JWT. As a result, the receiver can no longer verify the signature.", "type": "string"}}, "type": "object"}, "WorkforcePoolAllowedLocations": {"description": "Represents a list of allowed locations for given workforce pool.", "id": "WorkforcePoolAllowedLocations", "properties": {"encodedLocations": {"description": "Output only. The hex encoded bitmap of the trust boundary locations", "readOnly": true, "type": "string"}, "locations": {"description": "Output only. The human readable trust boundary locations. For example, [\"us-central1\", \"europe-west1\"]", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "WorkloadIdentityPoolAllowedLocations": {"description": "Represents a list of allowed locations for given workload identity pool.", "id": "WorkloadIdentityPoolAllowedLocations", "properties": {"encodedLocations": {"description": "Output only. The hex encoded bitmap of the trust boundary locations", "readOnly": true, "type": "string"}, "locations": {"description": "Output only. The human readable trust boundary locations. For example, [\"us-central1\", \"europe-west1\"]", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}}, "servicePath": "", "title": "IAM Service Account Credentials API", "version": "v1", "version_module": true}