{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "", "baseUrl": "https://merchantapi.googleapis.com/", "batchPath": "batch", "canonicalName": "Merchant", "description": "Programmatically manage your Merchant Center Accounts.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/merchant/api", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "merchantapi:ordertracking_v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://merchantapi.mtls.googleapis.com/", "name": "merchantapi", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"resources": {"orderTrackingSignals": {"methods": {"create": {"description": "Creates new order tracking signal.", "flatPath": "ordertracking/v1/accounts/{accountsId}/orderTrackingSignals", "httpMethod": "POST", "id": "merchantapi.accounts.orderTrackingSignals.create", "parameterOrder": ["parent"], "parameters": {"orderTrackingSignalId": {"description": "Output only. The ID that uniquely identifies this order tracking signal.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account of the business for which the order signal is created. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "ordertracking/v1/{+parent}/orderTrackingSignals", "request": {"$ref": "OrderTrackingSignal"}, "response": {"$ref": "OrderTrackingSignal"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}}, "revision": "********", "rootUrl": "https://merchantapi.googleapis.com/", "schemas": {"DateTime": {"description": "Represents civil time (or occasionally physical time). This type can represent a civil time in one of a few possible ways: * When utc_offset is set and time_zone is unset: a civil time on a calendar day with a particular offset from UTC. * When time_zone is set and utc_offset is unset: a civil time on a calendar day in a particular time zone. * When neither time_zone nor utc_offset is set: a civil time on a calendar day in local time. The date is relative to the Proleptic Gregorian Calendar. If year, month, or day are 0, the DateTime is considered not to have a specific year, month, or day respectively. This type may also be used to represent a physical time if all the date and time fields are set and either case of the `time_offset` oneof is set. Consider using `Timestamp` message for physical time instead. If your use case also would like to store the user's timezone, that can be done in another field. This type is more flexible than some applications may want. Make sure to document and validate your application's limitations.", "id": "DateTime", "properties": {"day": {"description": "Optional. Day of month. Must be from 1 to 31 and valid for the year and month, or 0 if specifying a datetime without a day.", "format": "int32", "type": "integer"}, "hours": {"description": "Optional. Hours of day in 24 hour format. Should be from 0 to 23, defaults to 0 (midnight). An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Optional. Minutes of hour of day. Must be from 0 to 59, defaults to 0.", "format": "int32", "type": "integer"}, "month": {"description": "Optional. Month of year. Must be from 1 to 12, or 0 if specifying a datetime without a month.", "format": "int32", "type": "integer"}, "nanos": {"description": "Optional. Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999, defaults to 0.", "format": "int32", "type": "integer"}, "seconds": {"description": "Optional. Seconds of minutes of the time. Must normally be from 0 to 59, defaults to 0. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}, "timeZone": {"$ref": "TimeZone", "description": "Time zone."}, "utcOffset": {"description": "UTC offset. Must be whole seconds, between -18 hours and +18 hours. For example, a UTC offset of -4:00 would be represented as { seconds: -14400 }.", "format": "google-duration", "type": "string"}, "year": {"description": "Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a datetime without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "LineItemDetails": {"description": "The line items of the order.", "id": "LineItemDetails", "properties": {"brand": {"description": "Optional. Brand of the product.", "type": "string"}, "gtins": {"description": "Optional. The Global Trade Item Numbers.", "items": {"type": "string"}, "type": "array"}, "lineItemId": {"description": "Required. The ID for this line item.", "type": "string"}, "mpn": {"description": "Optional. The manufacturer part number.", "type": "string"}, "productId": {"description": "Required. The Content API REST ID of the product, in the form channel:contentLanguage:targetCountry:offerId.", "type": "string"}, "productTitle": {"description": "Optional. Plain text title of this product.", "type": "string"}, "quantity": {"description": "Required. The quantity of the line item in the order.", "format": "int64", "type": "string"}}, "type": "object"}, "OrderTrackingSignal": {"description": "Represents a business trade from which signals are extracted, such as shipping.", "id": "OrderTrackingSignal", "properties": {"customerShippingFee": {"$ref": "Price", "description": "Optional. The shipping fee of the order; this value should be set to zero in the case of free shipping."}, "deliveryPostalCode": {"description": "Optional. The delivery postal code, as a continuous string without spaces or dashes, for example \"95016\". This field will be anonymized in returned OrderTrackingSignal creation response.", "type": "string"}, "deliveryRegionCode": {"description": "Optional. The [CLDR territory code] (http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml) for the shipping destination.", "type": "string"}, "lineItems": {"description": "Required. Information about line items in the order.", "items": {"$ref": "LineItemDetails"}, "type": "array"}, "merchantId": {"description": "Optional. The Google Merchant Center ID of this order tracking signal. This value is optional. If left unset, the caller's Merchant Center ID is used. You must request access in order to provide data on behalf of another business. For more information, see [Submitting Order Tracking Signals](/shopping-content/guides/order-tracking-signals).", "format": "int64", "type": "string"}, "orderCreatedTime": {"$ref": "DateTime", "description": "Required. The time when the order was created on the businesses side. Include the year and timezone string, if available."}, "orderId": {"description": "Required. The ID of the order on the businesses side. This field will be hashed in returned OrderTrackingSignal creation response.", "type": "string"}, "orderTrackingSignalId": {"description": "Output only. The ID that uniquely identifies this order tracking signal.", "format": "int64", "readOnly": true, "type": "string"}, "shipmentLineItemMapping": {"description": "Optional. The mapping of the line items to the shipment information.", "items": {"$ref": "ShipmentLineItemMapping"}, "type": "array"}, "shippingInfo": {"description": "Required. The shipping information for the order.", "items": {"$ref": "ShippingInfo"}, "type": "array"}}, "type": "object"}, "Price": {"description": "The price represented as a number and currency.", "id": "Price", "properties": {"amountMicros": {"description": "The price represented as a number in micros (1 million micros is an equivalent to one's currency standard unit, for example, 1 USD = 1000000 micros).", "format": "int64", "type": "string"}, "currencyCode": {"description": "The currency of the price using three-letter acronyms according to [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217).", "type": "string"}}, "type": "object"}, "ProductChange": {"description": "The change that happened to the product including old value, new value, country code as the region code and reporting context.", "id": "ProductChange", "properties": {"newValue": {"description": "The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "oldValue": {"description": "The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "regionCode": {"description": "Countries that have the change (if applicable). Represented in the ISO 3166 format.", "type": "string"}, "reportingContext": {"description": "Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/********).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}}, "type": "object"}, "ProductStatusChangeMessage": {"description": "The message that the merchant will receive to notify about product status change event", "id": "ProductStatusChangeMessage", "properties": {"account": {"description": "The target account that owns the entity that changed. Format : `accounts/{merchant_id}`", "type": "string"}, "attribute": {"description": "The attribute in the resource that changed, in this case it will be always `Status`.", "enum": ["ATTRIBUTE_UNSPECIFIED", "STATUS"], "enumDescriptions": ["Unspecified attribute", "Status of the changed entity"], "type": "string"}, "changes": {"description": "A message to describe the change that happened to the product", "items": {"$ref": "ProductChange"}, "type": "array"}, "eventTime": {"description": "The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.", "format": "google-datetime", "type": "string"}, "expirationTime": {"description": "Optional. The product expiration time. This field will not be set if the notification is sent for a product deletion event.", "format": "google-datetime", "type": "string"}, "managingAccount": {"description": "The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id}`", "type": "string"}, "resource": {"description": "The product name. Format: `accounts/{account}/products/{product}`", "type": "string"}, "resourceId": {"description": "The product id.", "type": "string"}, "resourceType": {"description": "The resource that changed, in this case it will always be `Product`.", "enum": ["RESOURCE_UNSPECIFIED", "PRODUCT"], "enumDescriptions": ["Unspecified resource", "Resource type : product"], "type": "string"}}, "type": "object"}, "ShipmentLineItemMapping": {"description": "Represents how many items are in the shipment for the given shipment_id and line_item_id.", "id": "ShipmentLineItemMapping", "properties": {"lineItemId": {"description": "Required. The line item ID.", "type": "string"}, "quantity": {"description": "Required. The line item quantity in the shipment.", "format": "int64", "type": "string"}, "shipmentId": {"description": "Required. The shipment ID. This field will be hashed in returned OrderTrackingSignal creation response.", "type": "string"}}, "type": "object"}, "ShippingInfo": {"description": "The shipping information for the order.", "id": "ShippingInfo", "properties": {"actualDeliveryTime": {"$ref": "DateTime", "description": "Optional. The time when the shipment was actually delivered. Include the year and timezone string, if available. This field is required, if one of the following fields is absent: tracking_id or carrier_name."}, "carrier": {"description": "Optional. The name of the shipping carrier for the delivery. This field is required if one of the following fields is absent: earliest_delivery_promise_time, latest_delivery_promise_time, and actual_delivery_time.", "type": "string"}, "carrierService": {"description": "Optional. The service type for fulfillment, such as GROUND, FIRST_CLASS, etc.", "type": "string"}, "earliestDeliveryPromiseTime": {"$ref": "DateTime", "description": "Optional. The earliest delivery promised time. Include the year and timezone string, if available. This field is required, if one of the following fields is absent: tracking_id or carrier_name."}, "latestDeliveryPromiseTime": {"$ref": "DateTime", "description": "Optional. The latest delivery promised time. Include the year and timezone string, if available. This field is required, if one of the following fields is absent: tracking_id or carrier_name."}, "originPostalCode": {"description": "Required. The origin postal code, as a continuous string without spaces or dashes, for example \"95016\". This field will be anonymized in returned OrderTrackingSignal creation response.", "type": "string"}, "originRegionCode": {"description": "Required. The [CLDR territory code] (http://www.unicode.org/repos/cldr/tags/latest/common/main/en.xml) for the shipping origin.", "type": "string"}, "shipmentId": {"description": "Required. The shipment ID. This field will be hashed in returned OrderTrackingSignal creation response.", "type": "string"}, "shippedTime": {"$ref": "DateTime", "description": "Optional. The time when the shipment was shipped. Include the year and timezone string, if available."}, "shippingStatus": {"description": "Required. The status of the shipment.", "enum": ["SHIPPING_STATE_UNSPECIFIED", "SHIPPED", "DELIVERED"], "enumDescriptions": ["The shipping status is not known to business.", "All items are shipped.", "The shipment is already delivered."], "type": "string"}, "trackingId": {"description": "Optional. The tracking ID of the shipment. This field is required if one of the following fields is absent: earliest_delivery_promise_time, latest_delivery_promise_time, and actual_delivery_time.", "type": "string"}}, "type": "object"}, "TimeZone": {"description": "Represents a time zone from the [IANA Time Zone Database](https://www.iana.org/time-zones).", "id": "TimeZone", "properties": {"id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "type": "string"}, "version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Merchant API", "version": "ordertracking_v1", "version_module": true}