{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/playdeveloperreporting": {"description": "See metrics and data about the apps in your Google Play Developer account"}}}}, "basePath": "", "baseUrl": "https://playdeveloperreporting.googleapis.com/", "batchPath": "batch", "canonicalName": "Playdeveloperreporting", "description": "", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/play/developer/reporting", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "playdeveloperreporting:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://playdeveloperreporting.mtls.googleapis.com/", "name": "playdeveloperreporting", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"anomalies": {"methods": {"list": {"description": "Lists anomalies in any of the datasets.", "flatPath": "v1beta1/apps/{appsId}/anomalies", "httpMethod": "GET", "id": "playdeveloperreporting.anomalies.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Filtering criteria for anomalies. For basic filter guidance, please check: https://google.aip.dev/160. **Supported functions:** * `activeBetween(startTime, endTime)`: If specified, only list anomalies that were active in between `startTime` (inclusive) and `endTime` (exclusive). Both parameters are expected to conform to an RFC-3339 formatted string (e.g. `2012-04-21T11:30:00-04:00`). UTC offsets are supported. Both `startTime` and `endTime` accept the special value `UNBOUNDED`, to signify intervals with no lower or upper bound, respectively. Examples: * `activeBetween(\"2021-04-21T11:30:00Z\", \"2021-07-21T00:00:00Z\")` * `activeBetween(UNBOUNDED, \"2021-11-21T00:00:00-04:00\")` * `activeBetween(\"2021-07-21T00:00:00-04:00\", UNBOUNDED)`", "location": "query", "type": "string"}, "pageSize": {"description": "Maximum size of the returned data. If unspecified, at most 10 anomalies will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `ListErrorReports` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListErrorReports` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent app for which anomalies were detected. Format: apps/{app}", "location": "path", "pattern": "^apps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/anomalies", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1ListAnomaliesResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "apps": {"methods": {"fetchReleaseFilterOptions": {"description": "Describes filtering options for releases.", "flatPath": "v1beta1/apps/{appsId}:fetchReleaseFilterOptions", "httpMethod": "GET", "id": "playdeveloperreporting.apps.fetchReleaseFilterOptions", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource, i.e. app the filtering options are for. Format: apps/{app}", "location": "path", "pattern": "^apps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:fetchReleaseFilterOptions", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1ReleaseFilterOptions"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "search": {"description": "Searches for Apps accessible by the user.", "flatPath": "v1beta1/apps:search", "httpMethod": "GET", "id": "playdeveloperreporting.apps.search", "parameterOrder": [], "parameters": {"pageSize": {"description": "The maximum number of apps to return. The service may return fewer than this value. If unspecified, at most 50 apps will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `SearchAccessibleApps` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `SearchAccessibleApps` must match the call that provided the page token.", "location": "query", "type": "string"}}, "path": "v1beta1/apps:search", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1SearchAccessibleAppsResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "vitals": {"resources": {"anrrate": {"methods": {"get": {"description": "Describes the properties of the metric set.", "flatPath": "v1beta1/apps/{appsId}/anrRateMetricSet", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.anrrate.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/anrRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/anrRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1AnrRateMetricSet"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "query": {"description": "Queries the metrics in the metric set.", "flatPath": "v1beta1/apps/{appsId}/anrRateMetricSet:query", "httpMethod": "POST", "id": "playdeveloperreporting.vitals.anrrate.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/anrRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/anrRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:query", "request": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryAnrRateMetricSetRequest"}, "response": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryAnrRateMetricSetResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "crashrate": {"methods": {"get": {"description": "Describes the properties of the metric set.", "flatPath": "v1beta1/apps/{appsId}/crashRateMetricSet", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.crashrate.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/crashRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/crashRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1CrashRateMetricSet"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "query": {"description": "Queries the metrics in the metric set.", "flatPath": "v1beta1/apps/{appsId}/crashRateMetricSet:query", "httpMethod": "POST", "id": "playdeveloperreporting.vitals.crashrate.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/crashRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/crashRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:query", "request": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryCrashRateMetricSetRequest"}, "response": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryCrashRateMetricSetResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "errors": {"resources": {"counts": {"methods": {"get": {"description": "Describes the properties of the metrics set.", "flatPath": "v1beta1/apps/{appsId}/errorCountMetricSet", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.errors.counts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the errors metric set. Format: apps/{app}/errorCountMetricSet", "location": "path", "pattern": "^apps/[^/]+/errorCountMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1ErrorCountMetricSet"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "query": {"description": "Queries the metrics in the metrics set.", "flatPath": "v1beta1/apps/{appsId}/errorCountMetricSet:query", "httpMethod": "POST", "id": "playdeveloperreporting.vitals.errors.counts.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/errorCountMetricSet", "location": "path", "pattern": "^apps/[^/]+/errorCountMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:query", "request": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryErrorCountMetricSetRequest"}, "response": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryErrorCountMetricSetResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "issues": {"methods": {"search": {"description": "Searches all error issues in which reports have been grouped.", "flatPath": "v1beta1/apps/{appsId}/errorIssues:search", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.errors.issues.search", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A selection predicate to retrieve only a subset of the issues. Counts in the returned error issues will only reflect occurrences that matched the filter. For filtering basics, please check [AIP-160](https://google.aip.dev/160). ** Supported field names:** * `apiLevel`: Matches error issues that occurred in the requested Android versions (specified as the numeric API level) only. Example: `apiLevel = 28 OR apiLevel = 29`. * `versionCode`: Matches error issues that occurred in the requested app version codes only. Example: `versionCode = 123 OR versionCode = 456`. * `deviceModel`: Matches error issues that occurred in the requested devices. Example: `deviceModel = \"google/walleye\" OR deviceModel = \"google/marlin\"`. * `deviceBrand`: Matches error issues that occurred in the requested device brands. Example: `deviceBrand = \"Google\". * `deviceType`: Matches error issues that occurred in the requested device types. Example: `deviceType = \"PHONE\"`. * `errorIssueType`: Matches error issues of the requested types only. Valid candidates: `CRASH`, `ANR`, `NON_FATAL`. Example: `errorIssueType = CRASH OR errorIssueType = ANR`. * `appProcessState`: Matches error issues on the process state of an app, indicating whether an app runs in the foreground (user-visible) or background. Valid candidates: `FOREGROUND`, `BACKGROUND`. Example: `appProcessState = FOREGROUND`. * `isUserPerceived`: Matches error issues that are user-perceived. It is not accompanied by any operators. Example: `isUserPerceived`. ** Supported operators:** * Comparison operators: The only supported comparison operator is equality. The filtered field must appear on the left hand side of the comparison. * Logical Operators: Logical operators `AND` and `OR` can be used to build complex filters following a conjunctive normal form (CNF), i.e., conjunctions of disjunctions. The `OR` operator takes precedence over `AND` so the use of parenthesis is not necessary when building CNF. The `OR` operator is only supported to build disjunctions that apply to the same field, e.g., `versionCode = 123 OR errorIssueType = ANR` is not a valid filter. ** Examples ** Some valid filtering expressions: * `versionCode = 123 AND errorIssueType = ANR` * `versionCode = 123 AND errorIssueType = OR errorIssueType = CRASH` * `versionCode = 123 AND (errorIssueType = OR errorIssueType = CRASH)`", "location": "query", "type": "string"}, "interval.endTime.day": {"description": "Optional. Day of month. Must be from 1 to 31 and valid for the year and month, or 0 if specifying a datetime without a day.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.hours": {"description": "Optional. Hours of day in 24 hour format. Should be from 0 to 23, defaults to 0 (midnight). An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.minutes": {"description": "Optional. Minutes of hour of day. Must be from 0 to 59, defaults to 0.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.month": {"description": "Optional. Month of year. Must be from 1 to 12, or 0 if specifying a datetime without a month.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.nanos": {"description": "Optional. Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999, defaults to 0.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.seconds": {"description": "Optional. Seconds of minutes of the time. Must normally be from 0 to 59, defaults to 0. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.timeZone.id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "location": "query", "type": "string"}, "interval.endTime.timeZone.version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "location": "query", "type": "string"}, "interval.endTime.utcOffset": {"description": "UTC offset. Must be whole seconds, between -18 hours and +18 hours. For example, a UTC offset of -4:00 would be represented as { seconds: -14400 }.", "format": "google-duration", "location": "query", "type": "string"}, "interval.endTime.year": {"description": "Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a datetime without a year.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.day": {"description": "Optional. Day of month. Must be from 1 to 31 and valid for the year and month, or 0 if specifying a datetime without a day.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.hours": {"description": "Optional. Hours of day in 24 hour format. Should be from 0 to 23, defaults to 0 (midnight). An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.minutes": {"description": "Optional. Minutes of hour of day. Must be from 0 to 59, defaults to 0.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.month": {"description": "Optional. Month of year. Must be from 1 to 12, or 0 if specifying a datetime without a month.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.nanos": {"description": "Optional. Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999, defaults to 0.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.seconds": {"description": "Optional. Seconds of minutes of the time. Must normally be from 0 to 59, defaults to 0. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.timeZone.id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "location": "query", "type": "string"}, "interval.startTime.timeZone.version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "location": "query", "type": "string"}, "interval.startTime.utcOffset": {"description": "UTC offset. Must be whole seconds, between -18 hours and +18 hours. For example, a UTC offset of -4:00 would be represented as { seconds: -14400 }.", "format": "google-duration", "location": "query", "type": "string"}, "interval.startTime.year": {"description": "Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a datetime without a year.", "format": "int32", "location": "query", "type": "integer"}, "orderBy": {"description": "Specifies a field that will be used to order the results. ** Supported dimensions:** * `errorReportCount`: Orders issues by number of error reports. * `distinctUsers`: Orders issues by number of unique affected users. ** Supported operations:** * `asc` for ascending order. * `desc` for descending order. Format: A field and an operation, e.g., `errorReportCount desc` *Note:* currently only one field is supported at a time.", "location": "query", "type": "string"}, "pageSize": {"description": "The maximum number of error issues to return. The service may return fewer than this value. If unspecified, at most 50 error issues will be returned. The maximum value is 1000; values above 1000 will be coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource of the error issues, indicating the application for which they were received. Format: apps/{app}", "location": "path", "pattern": "^apps/[^/]+$", "required": true, "type": "string"}, "sampleErrorReportLimit": {"description": "Optional. Number of sample error reports to return per ErrorIssue. If unspecified, 0 will be used. *Note:* currently only 0 and 1 are supported.", "format": "int32", "location": "query", "type": "integer"}}, "path": "v1beta1/{+parent}/errorIssues:search", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1SearchErrorIssuesResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "reports": {"methods": {"search": {"description": "Searches all error reports received for an app.", "flatPath": "v1beta1/apps/{appsId}/errorReports:search", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.errors.reports.search", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "A selection predicate to retrieve only a subset of the reports. For filtering basics, please check [AIP-160](https://google.aip.dev/160). ** Supported field names:** * `apiLevel`: Matches error reports that occurred in the requested Android versions (specified as the numeric API level) only. Example: `apiLevel = 28 OR apiLevel = 29`. * `versionCode`: Matches error reports that occurred in the requested app version codes only. Example: `versionCode = 123 OR versionCode = 456`. * `deviceModel`: Matches error issues that occurred in the requested devices. Example: `deviceModel = \"google/walleye\" OR deviceModel = \"google/marlin\"`. * `deviceBrand`: Matches error issues that occurred in the requested device brands. Example: `deviceBrand = \"Google\". * `deviceType`: Matches error reports that occurred in the requested device types. Example: `deviceType = \"PHONE\"`. * `errorIssueType`: Matches error reports of the requested types only. Valid candidates: `CRASH`, `ANR`, `NON_FATAL`. Example: `errorIssueType = CRASH OR errorIssueType = ANR`. * `errorIssueId`: Matches error reports belonging to the requested error issue ids only. Example: `errorIssueId = 1234 OR errorIssueId = 4567`. * `errorReportId`: Matches error reports with the requested error report id. Example: `errorReportId = 1234 OR errorReportId = 4567`. * `appProcessState`: Matches error reports on the process state of an app, indicating whether an app runs in the foreground (user-visible) or background. Valid candidates: `FOREGROUND`, `BACKGROUND`. Example: `appProcessState = FOREGROUND`. * `isUserPerceived`: Matches error reports that are user-perceived. It is not accompanied by any operators. Example: `isUserPerceived`. ** Supported operators:** * Comparison operators: The only supported comparison operator is equality. The filtered field must appear on the left hand side of the comparison. * Logical Operators: Logical operators `AND` and `OR` can be used to build complex filters following a conjunctive normal form (CNF), i.e., conjunctions of disjunctions. The `OR` operator takes precedence over `AND` so the use of parenthesis is not necessary when building CNF. The `OR` operator is only supported to build disjunctions that apply to the same field, e.g., `versionCode = 123 OR versionCode = ANR`. The filter expression `versionCode = 123 OR errorIssueType = ANR` is not valid. ** Examples ** Some valid filtering expressions: * `versionCode = 123 AND errorIssueType = ANR` * `versionCode = 123 AND errorIssueType = OR errorIssueType = CRASH` * `versionCode = 123 AND (errorIssueType = OR errorIssueType = CRASH)`", "location": "query", "type": "string"}, "interval.endTime.day": {"description": "Optional. Day of month. Must be from 1 to 31 and valid for the year and month, or 0 if specifying a datetime without a day.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.hours": {"description": "Optional. Hours of day in 24 hour format. Should be from 0 to 23, defaults to 0 (midnight). An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.minutes": {"description": "Optional. Minutes of hour of day. Must be from 0 to 59, defaults to 0.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.month": {"description": "Optional. Month of year. Must be from 1 to 12, or 0 if specifying a datetime without a month.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.nanos": {"description": "Optional. Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999, defaults to 0.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.seconds": {"description": "Optional. Seconds of minutes of the time. Must normally be from 0 to 59, defaults to 0. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "location": "query", "type": "integer"}, "interval.endTime.timeZone.id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "location": "query", "type": "string"}, "interval.endTime.timeZone.version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "location": "query", "type": "string"}, "interval.endTime.utcOffset": {"description": "UTC offset. Must be whole seconds, between -18 hours and +18 hours. For example, a UTC offset of -4:00 would be represented as { seconds: -14400 }.", "format": "google-duration", "location": "query", "type": "string"}, "interval.endTime.year": {"description": "Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a datetime without a year.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.day": {"description": "Optional. Day of month. Must be from 1 to 31 and valid for the year and month, or 0 if specifying a datetime without a day.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.hours": {"description": "Optional. Hours of day in 24 hour format. Should be from 0 to 23, defaults to 0 (midnight). An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.minutes": {"description": "Optional. Minutes of hour of day. Must be from 0 to 59, defaults to 0.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.month": {"description": "Optional. Month of year. Must be from 1 to 12, or 0 if specifying a datetime without a month.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.nanos": {"description": "Optional. Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999, defaults to 0.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.seconds": {"description": "Optional. Seconds of minutes of the time. Must normally be from 0 to 59, defaults to 0. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "location": "query", "type": "integer"}, "interval.startTime.timeZone.id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "location": "query", "type": "string"}, "interval.startTime.timeZone.version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "location": "query", "type": "string"}, "interval.startTime.utcOffset": {"description": "UTC offset. Must be whole seconds, between -18 hours and +18 hours. For example, a UTC offset of -4:00 would be represented as { seconds: -14400 }.", "format": "google-duration", "location": "query", "type": "string"}, "interval.startTime.year": {"description": "Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a datetime without a year.", "format": "int32", "location": "query", "type": "integer"}, "pageSize": {"description": "The maximum number of reports to return. The service may return fewer than this value. If unspecified, at most 50 reports will be returned. The maximum value is 100; values above 100 will be coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous `SearchErrorReports` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `SearchErrorReports` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. Parent resource of the reports, indicating the application for which they were received. Format: apps/{app}", "location": "path", "pattern": "^apps/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/errorReports:search", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1SearchErrorReportsResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}}}, "excessivewakeuprate": {"methods": {"get": {"description": "Describes the properties of the metric set.", "flatPath": "v1beta1/apps/{appsId}/excessiveWakeupRateMetricSet", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.excessivewakeuprate.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/excessiveWakeupRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/excessiveWakeupRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1ExcessiveWakeupRateMetricSet"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "query": {"description": "Queries the metrics in the metric set.", "flatPath": "v1beta1/apps/{appsId}/excessiveWakeupRateMetricSet:query", "httpMethod": "POST", "id": "playdeveloperreporting.vitals.excessivewakeuprate.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/excessiveWakeupRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/excessiveWakeupRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:query", "request": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryExcessiveWakeupRateMetricSetRequest"}, "response": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryExcessiveWakeupRateMetricSetResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "lmkrate": {"methods": {"get": {"description": "Describes the properties of the metric set.", "flatPath": "v1beta1/apps/{appsId}/lmkRateMetricSet", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.lmkrate.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/lmkRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/lmkRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1LmkRateMetricSet"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "query": {"description": "Queries the metrics in the metric set.", "flatPath": "v1beta1/apps/{appsId}/lmkRateMetricSet:query", "httpMethod": "POST", "id": "playdeveloperreporting.vitals.lmkrate.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/lmkRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/lmkRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:query", "request": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryLmkRateMetricSetRequest"}, "response": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryLmkRateMetricSetResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "slowrenderingrate": {"methods": {"get": {"description": "Describes the properties of the metric set.", "flatPath": "v1beta1/apps/{appsId}/slowRenderingRateMetricSet", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.slowrenderingrate.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/slowRenderingRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/slowRenderingRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1SlowRenderingRateMetricSet"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "query": {"description": "Queries the metrics in the metric set.", "flatPath": "v1beta1/apps/{appsId}/slowRenderingRateMetricSet:query", "httpMethod": "POST", "id": "playdeveloperreporting.vitals.slowrenderingrate.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/slowRenderingRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/slowRenderingRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:query", "request": {"$ref": "GooglePlayDeveloperReportingV1beta1QuerySlowRenderingRateMetricSetRequest"}, "response": {"$ref": "GooglePlayDeveloperReportingV1beta1QuerySlowRenderingRateMetricSetResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "slowstartrate": {"methods": {"get": {"description": "Describes the properties of the metric set.", "flatPath": "v1beta1/apps/{appsId}/slowStartRateMetricSet", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.slowstartrate.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/slowStartRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/slowStartRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1SlowStartRateMetricSet"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "query": {"description": "Queries the metrics in the metric set.", "flatPath": "v1beta1/apps/{appsId}/slowStartRateMetricSet:query", "httpMethod": "POST", "id": "playdeveloperreporting.vitals.slowstartrate.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/slowStartRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/slowStartRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:query", "request": {"$ref": "GooglePlayDeveloperReportingV1beta1QuerySlowStartRateMetricSetRequest"}, "response": {"$ref": "GooglePlayDeveloperReportingV1beta1QuerySlowStartRateMetricSetResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}, "stuckbackgroundwakelockrate": {"methods": {"get": {"description": "Describes the properties of the metric set.", "flatPath": "v1beta1/apps/{appsId}/stuckBackgroundWakelockRateMetricSet", "httpMethod": "GET", "id": "playdeveloperreporting.vitals.stuckbackgroundwakelockrate.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/stuckBackgroundWakelockRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/stuckBackgroundWakelockRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GooglePlayDeveloperReportingV1beta1StuckBackgroundWakelockRateMetricSet"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}, "query": {"description": "Queries the metrics in the metric set.", "flatPath": "v1beta1/apps/{appsId}/stuckBackgroundWakelockRateMetricSet:query", "httpMethod": "POST", "id": "playdeveloperreporting.vitals.stuckbackgroundwakelockrate.query", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name. Format: apps/{app}/stuckBackgroundWakelockRateMetricSet", "location": "path", "pattern": "^apps/[^/]+/stuckBackgroundWakelockRateMetricSet$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:query", "request": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryStuckBackgroundWakelockRateMetricSetRequest"}, "response": {"$ref": "GooglePlayDeveloperReportingV1beta1QueryStuckBackgroundWakelockRateMetricSetResponse"}, "scopes": ["https://www.googleapis.com/auth/playdeveloperreporting"]}}}}}}, "revision": "20250311", "rootUrl": "https://playdeveloperreporting.googleapis.com/", "schemas": {"GooglePlayDeveloperReportingV1beta1Anomaly": {"description": "Represents an anomaly detected in a dataset. Our anomaly detection systems flag datapoints in a time series that fall outside of and expected range derived from historical data. Although those expected ranges have an upper and a lower bound, we only flag anomalies when the data has become unexpectedly _worse_, which usually corresponds to the case where the metric crosses the upper bound. Multiple contiguous datapoints in a timeline outside of the expected range will be grouped into a single anomaly. Therefore, an anomaly represents effectively a segment of a metric's timeline. The information stored in the `timeline_spec`, `dimensions` and `metric` can be used to fetch a full timeline with extended ragne for context. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app.", "id": "GooglePlayDeveloperReportingV1beta1Anomaly", "properties": {"dimensions": {"description": "Combination of dimensions in which the anomaly was detected.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1DimensionValue"}, "type": "array"}, "metric": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricValue", "description": "Metric where the anomaly was detected, together with the anomalous value."}, "metricSet": {"description": "Metric set resource where the anomaly was detected.", "type": "string"}, "name": {"description": "Identifier. Name of the anomaly. Format: apps/{app}/anomalies/{anomaly}", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Timeline specification that covers the anomaly period."}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1AnrRateMetricSet": {"description": "Singleton resource representing the set of ANR (Application not responding) metrics. This metric set contains ANRs data combined with usage data to produce a normalized metric independent of user counts. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`. * HOURLY: metrics are aggregated in hourly intervals. The default and only supported timezone is `UTC`. **Supported metrics:** * `anrRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one ANR. * `anrRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `anrRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `anrRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `anrRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `userPerceivedAnrRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one user-perceived ANR. User-perceived ANRs are currently those of 'Input dispatching' type. * `userPerceivedAnrRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedAnrRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `userPerceivedAnrRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedAnrRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `anrRate` and `userPerceivedAnrRate` metrics. A user is counted in this metric if they used the app in the foreground during the aggregation period. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app. **Related metric sets:** * vitals.errors contains unnormalized version (absolute counts) of crashes. * vitals.errors contains normalized metrics about crashes, another stability metric.", "id": "GooglePlayDeveloperReportingV1beta1AnrRateMetricSet", "properties": {"freshnessInfo": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "description": "Summary about data freshness in this resource."}, "name": {"description": "Identifier. The resource name. Format: apps/{app}/anrRateMetricSet", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1App": {"description": "A representation of an app in the Play Store.", "id": "GooglePlayDeveloperReportingV1beta1App", "properties": {"displayName": {"description": "Title of the app. This is the latest title as set in the Play Console and may not yet have been reviewed, so might not match the Play Store. Example: `Google Maps`.", "type": "string"}, "name": {"description": "Identifier. The resource name. Format: apps/{app}", "type": "string"}, "packageName": {"description": "Package name of the app. Example: `com.example.app123`.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1AppVersion": {"description": "Representations of an app version.", "id": "GooglePlayDeveloperReportingV1beta1AppVersion", "properties": {"versionCode": {"description": "Numeric version code of the app version (set by the app's developer).", "format": "int64", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1CrashRateMetricSet": {"description": "Singleton resource representing the set of crashrate metrics. This metric set contains crashes data combined with usage data to produce a normalized metric independent of user counts. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`. * HOURLY: metrics are aggregated in hourly intervals. The default and only supported timezone is `UTC`. **Supported metrics:** * `crashRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one crash. * `crashRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `crashRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `crashRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `crashRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `userPerceivedCrashRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one crash while they were actively using your app (a user-perceived crash). An app is considered to be in active use if it is displaying any activity or executing any foreground service. * `userPerceivedCrashRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedCrashRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `userPerceivedCrashRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedCrashRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `crashRate` and `userPerceivedCrashRate` metrics. A user is counted in this metric if they used the app actively during the aggregation period. An app is considered to be in active use if it is displaying any activity or executing any foreground service. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app. **Related metric sets:** * vitals.errors contains unnormalized version (absolute counts) of crashes. * vitals.errors contains normalized metrics about ANRs, another stability metric.", "id": "GooglePlayDeveloperReportingV1beta1CrashRateMetricSet", "properties": {"freshnessInfo": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "description": "Summary about data freshness in this resource."}, "name": {"description": "Identifier. The resource name. Format: apps/{app}/crashRateMetricSet", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1DecimalConfidenceInterval": {"description": "Represents the confidence interval of a metric.", "id": "GooglePlayDeveloperReportingV1beta1DecimalConfidenceInterval", "properties": {"lowerBound": {"$ref": "GoogleTypeDecimal", "description": "The confidence interval's lower bound."}, "upperBound": {"$ref": "GoogleTypeDecimal", "description": "The confidence interval's upper bound."}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1DeviceId": {"description": "Identifier of a device.", "id": "GooglePlayDeveloperReportingV1beta1DeviceId", "properties": {"buildBrand": {"description": "Value of Build.BRAND.", "type": "string"}, "buildDevice": {"description": "Value of Build.DEVICE.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1DeviceModelSummary": {"description": "Summary of a device", "id": "GooglePlayDeveloperReportingV1beta1DeviceModelSummary", "properties": {"deviceId": {"$ref": "GooglePlayDeveloperReportingV1beta1DeviceId", "description": "Identifier of the device."}, "deviceUri": {"description": "Link to the device in Play Device Catalog.", "type": "string"}, "marketingName": {"description": "Display name of the device.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1DimensionValue": {"description": "Represents the value of a single dimension.", "id": "GooglePlayDeveloperReportingV1beta1DimensionValue", "properties": {"dimension": {"description": "Name of the dimension.", "type": "string"}, "int64Value": {"description": "Actual value, represented as an int64.", "format": "int64", "type": "string"}, "stringValue": {"description": "Actual value, represented as a string.", "type": "string"}, "valueLabel": {"description": "Optional. Human-friendly label for the value, always in English. For example, 'Spain' for the 'ES' country code. Whereas the dimension value is stable, this value label is subject to change. Do not assume that the (value, value_label) relationship is stable. For example, the ISO country code 'MK' changed its name recently to 'North Macedonia'.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1ErrorCountMetricSet": {"description": "Singleton resource representing the set of error report metrics. This metric set contains un-normalized error report counts. **Supported aggregation periods:** * HOURLY: metrics are aggregated in hourly intervals. The default and only supported timezone is `UTC`. * DAILY: metrics are aggregated in calendar date intervals. The default and only supported timezone is `America/Los_Angeles`. **Supported metrics:** * `errorReportCount` (`google.type.Decimal`): Absolute count of individual error reports that have been received for an app. * `distinctUsers` (`google.type.Decimal`): Count of distinct users for which reports have been received. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. This value is not rounded, however it may be an approximation. **Required dimension:** This dimension must be always specified in all requests in the `dimensions` field in query requests. * `reportType` (string): the type of error. The value should correspond to one of the possible values in ErrorType. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceType` (string): identifier of the device's form factor, e.g., PHONE. * `issueId` (string): the id an error was assigned to. The value should correspond to the `{issue}` component of the issue name. * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app. **Related metric sets:** * vitals.errors.counts contains normalized metrics about Crashes, another stability metric. * vitals.errors.counts contains normalized metrics about ANRs, another stability metric.", "id": "GooglePlayDeveloperReportingV1beta1ErrorCountMetricSet", "properties": {"freshnessInfo": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "description": "Summary about data freshness in this resource."}, "name": {"description": "The resource name. Format: apps/{app}/errorCountMetricSet", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1ErrorIssue": {"description": "A group of related ErrorReports received for an app. Similar error reports are grouped together into issues with a likely identical root cause. **Please note:** this resource is currently in Alpha. There could be changes to the issue grouping that would result in similar but more recent error reports being assigned to different issues. This could also cause some issues disappearing entirely and being replaced by new ones. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app.", "id": "GooglePlayDeveloperReportingV1beta1ErrorIssue", "properties": {"annotations": {"description": "List of annotations for an issue. Annotations provide additional information that may help in diagnosing and fixing the issue.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1IssueAnnotation"}, "type": "array"}, "cause": {"description": "Cause of the issue. Depending on the type this can be either: * APPLICATION_NOT_RESPONDING: the type of ANR that occurred, e.g., 'Input dispatching timed out'. * CRASH: for Java unhandled exception errors, the type of the innermost exception that was thrown, e.g., IllegalArgumentException. For signals in native code, the signal that was raised, e.g. SIGSEGV.", "type": "string"}, "distinctUsers": {"description": "An estimate of the number of unique users who have experienced this issue (only considering occurrences matching the filters and within the requested time period).", "format": "int64", "type": "string"}, "distinctUsersPercent": {"$ref": "GoogleTypeDecimal", "description": "An estimated percentage of users affected by any issue that are affected by this issue (only considering occurrences matching the filters and within the requested time period)."}, "errorReportCount": {"description": "The total number of error reports in this issue (only considering occurrences matching the filters and within the requested time period).", "format": "int64", "type": "string"}, "firstAppVersion": {"$ref": "GooglePlayDeveloperReportingV1beta1AppVersion", "description": "The earliest (inclusive) app version appearing in this ErrorIssue in the requested time period (only considering occurrences matching the filters)."}, "firstOsVersion": {"$ref": "GooglePlayDeveloperReportingV1beta1OsVersion", "description": "The smallest OS version in which this error cluster has occurred in the requested time period (only considering occurrences matching the filters and within the requested time period)."}, "issueUri": {"description": "Link to the issue in Android vitals in the Play Console.", "type": "string"}, "lastAppVersion": {"$ref": "GooglePlayDeveloperReportingV1beta1AppVersion", "description": "The latest (inclusive) app version appearing in this ErrorIssue in the requested time period (only considering occurrences matching the filters)."}, "lastErrorReportTime": {"description": "Start of the hour during which the last error report in this issue occurred.", "format": "google-datetime", "type": "string"}, "lastOsVersion": {"$ref": "GooglePlayDeveloperReportingV1beta1OsVersion", "description": "The latest OS version in which this error cluster has occurred in the requested time period (only considering occurrences matching the filters and within the requested time period)."}, "location": {"description": "Location where the issue happened. Depending on the type this can be either: * APPLICATION_NOT_RESPONDING: the name of the activity or service that stopped responding. * CRASH: the likely method name that caused the error.", "type": "string"}, "name": {"description": "Identifier. The resource name of the issue. Format: apps/{app}/{issue}", "type": "string"}, "sampleErrorReports": {"description": "Output only. Sample error reports which belong to this ErrorIssue. *Note:* currently a maximum of 1 per ErrorIssue is supported. Format: \"apps/{app}/{report}\"", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "type": {"description": "Type of the errors grouped in this issue.", "enum": ["ERROR_TYPE_UNSPECIFIED", "APPLICATION_NOT_RESPONDING", "CRASH", "NON_FATAL"], "enumDescriptions": ["Unspecified error type.", "Application Not Responding (ANR) error. To learn more about this type of errors visit the corresponding Android Developers documentation.", "Crash caused by an unhandled exception in Java (or Kotlin or any other JVM language) or a signal in native code such as SIGSEGV.", "Non-fatal caused by events that do not immediately cause crashes, but is likely to lead to one."], "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1ErrorReport": {"description": "An error report received for an app. There reports are produced by the Android platform code when a (potentially fatal) error condition is detected. Identical reports from many users will be deduplicated and coalesced into a single ErrorReport. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app.", "id": "GooglePlayDeveloperReportingV1beta1ErrorReport", "properties": {"appVersion": {"$ref": "GooglePlayDeveloperReportingV1beta1AppVersion", "description": "The app version on which an event in this error report occurred on."}, "deviceModel": {"$ref": "GooglePlayDeveloperReportingV1beta1DeviceModelSummary", "description": "A device model on which an event in this error report occurred on."}, "eventTime": {"description": "Start of the hour during which the latest event in this error report occurred.", "format": "google-datetime", "type": "string"}, "issue": {"description": "The issue this report was associated with. **Please note:** this resource is currently in Alpha. There could be changes to the issue grouping that would result in similar but more recent error reports being assigned to a different issue.", "type": "string"}, "name": {"description": "The resource name of the report. Format: apps/{app}/{report}", "type": "string"}, "osVersion": {"$ref": "GooglePlayDeveloperReportingV1beta1OsVersion", "description": "The OS version on which an event in this error report occurred on."}, "reportText": {"description": "Textual representation of the error report. These textual reports are produced by the platform. The reports are then sanitized and filtered to remove any potentially sensitive information. Although their format is fairly stable, they are not entirely meant for machine consumption and we cannot guarantee that there won't be subtle changes to the formatting that may break systems trying to parse information out of the reports.", "type": "string"}, "type": {"description": "Type of the error for which this report was generated.", "enum": ["ERROR_TYPE_UNSPECIFIED", "APPLICATION_NOT_RESPONDING", "CRASH", "NON_FATAL"], "enumDescriptions": ["Unspecified error type.", "Application Not Responding (ANR) error. To learn more about this type of errors visit the corresponding Android Developers documentation.", "Crash caused by an unhandled exception in Java (or Kotlin or any other JVM language) or a signal in native code such as SIGSEGV.", "Non-fatal caused by events that do not immediately cause crashes, but is likely to lead to one."], "type": "string"}, "vcsInformation": {"description": "Version control system information from BUNDLE-METADATA/version-control-info.textproto or META-INF/version-control-info.textproto of the app bundle or APK, respectively.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1ExcessiveWakeupRateMetricSet": {"description": "Singleton resource representing the set of Excessive Weakeups metrics. This metric set contains AlarmManager wakeup counts data combined with process state data to produce a normalized metric independent of user counts. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`. **Supported metrics:** * `excessiveWakeupRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had more than 10 wakeups per hour. * `excessiveWakeupRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `excessiveWakeupRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `excessiveWakeupRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `excessiveWakeupRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `excessiveWakeupRate` metric. A user is counted in this metric if they app was doing any work on the device, i.e., not just active foreground usage but also background work. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app.", "id": "GooglePlayDeveloperReportingV1beta1ExcessiveWakeupRateMetricSet", "properties": {"freshnessInfo": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "description": "Summary about data freshness in this resource."}, "name": {"description": "Identifier. The resource name. Format: apps/{app}/excessiveWakeupRateMetricSet", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1FreshnessInfo": {"description": "Represents the latest available time that can be requested in a TimelineSpec. Different aggregation periods have different freshness. For example, `DAILY` aggregation may lag behind `HOURLY` in cases where such aggregation is computed only once at the end of the day.", "id": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "properties": {"freshnesses": {"description": "Information about data freshness for every supported aggregation period. This field has set semantics, keyed by the `aggregation_period` field.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfoFreshness"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1FreshnessInfoFreshness": {"description": "Information about data freshness for a single aggregation period.", "id": "GooglePlayDeveloperReportingV1beta1FreshnessInfoFreshness", "properties": {"aggregationPeriod": {"description": "Aggregation period for which data is available.", "enum": ["AGGREGATION_PERIOD_UNSPECIFIED", "HOURLY", "DAILY", "FULL_RANGE"], "enumDescriptions": ["Unspecified granularity.", "Data is aggregated in hourly intervals.", "Data is aggregated in daily intervals.", "Data is aggregated over the full timeline range. Effectively this produces a single value rather than a timeline."], "type": "string"}, "latestEndTime": {"$ref": "GoogleTypeDateTime", "description": "Latest end time for which data is available, for the aggregation period. The time is specified in the metric set's default timezone. *Note:* time ranges in TimelineSpec are represented as `start_time, end_time)`. For example, if the latest available timeline data point for a `DAILY` aggregation period is `2021-06-23 00:00:00 America/Los_Angeles`, the value of this field would be `2021-06-24 00:00:00 America/Los_Angeles` so it can be easily reused in [TimelineSpec.end_time."}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1IssueAnnotation": {"description": "Representation of an annotation message for an issue.", "id": "GooglePlayDeveloperReportingV1beta1IssueAnnotation", "properties": {"body": {"description": "Contains the contents of the annotation message.", "type": "string"}, "category": {"description": "Category that the annotation belongs to. An annotation will belong to a single category. Example categories: \"Potential fix\", \"Insight\".", "type": "string"}, "title": {"description": "Title for the annotation.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1ListAnomaliesResponse": {"description": "Response with a list of anomalies in datasets.", "id": "GooglePlayDeveloperReportingV1beta1ListAnomaliesResponse", "properties": {"anomalies": {"description": "Anomalies that were found.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1Anomaly"}, "type": "array"}, "nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1LmkRateMetricSet": {"description": "Singleton resource representing the set of LMK (Low Memory Kill) metrics. This metric set contains LMKs data combined with usage data to produce a normalized metric independent of user counts. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`. **Supported metrics:** * `userPerceivedLmkRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one LMK while they were actively using your app (a user-perceived LMK). An app is considered to be in active use if it is displaying any activity or executing any foreground service. * `userPerceivedLmkRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedLmkRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `userPerceivedLmkRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedLmkRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `userPerceivedLmkRate` metrics. A user is counted in this metric if they used the app in the foreground during the aggregation period. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app. **Related metric sets:** * vitals.errors contains normalized metrics about crashes, another stability metric. * vitals.errors contains normalized metrics about ANRs, another stability metric.", "id": "GooglePlayDeveloperReportingV1beta1LmkRateMetricSet", "properties": {"freshnessInfo": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "description": "Summary about data freshness in this resource."}, "name": {"description": "Identifier. The resource name. Format: apps/{app}/lmkRateMetricSet", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1MetricValue": {"description": "Represents the value of a metric.", "id": "GooglePlayDeveloperReportingV1beta1MetricValue", "properties": {"decimalValue": {"$ref": "GoogleTypeDecimal", "description": "Actual value, represented as a decimal number."}, "decimalValueConfidenceInterval": {"$ref": "GooglePlayDeveloperReportingV1beta1DecimalConfidenceInterval", "description": "Confidence interval of a value that is of type `type.Decimal`."}, "metric": {"description": "Name of the metric.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1MetricsRow": {"description": "Represents a row of dimensions and metrics.", "id": "GooglePlayDeveloperReportingV1beta1MetricsRow", "properties": {"aggregationPeriod": {"description": "Granularity of the aggregation period of the row.", "enum": ["AGGREGATION_PERIOD_UNSPECIFIED", "HOURLY", "DAILY", "FULL_RANGE"], "enumDescriptions": ["Unspecified granularity.", "Data is aggregated in hourly intervals.", "Data is aggregated in daily intervals.", "Data is aggregated over the full timeline range. Effectively this produces a single value rather than a timeline."], "type": "string"}, "dimensions": {"description": "Dimension columns in the row.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1DimensionValue"}, "type": "array"}, "metrics": {"description": "Metric columns in the row.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricValue"}, "type": "array"}, "startTime": {"$ref": "GoogleTypeDateTime", "description": "Starting date (and time for hourly aggregation) of the period covered by this row."}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1OsVersion": {"description": "Representation of an OS version.", "id": "GooglePlayDeveloperReportingV1beta1OsVersion", "properties": {"apiLevel": {"description": "Numeric version code of the OS - API level", "format": "int64", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryAnrRateMetricSetRequest": {"description": "Request message for QueryAnrRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryAnrRateMetricSetRequest", "properties": {"dimensions": {"description": "Dimensions to slice the metrics by. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi.", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "Filters to apply to data. The filtering expression follows [AIP-160](https://google.aip.dev/160) standard and supports filtering by equality of all breakdown dimensions.", "type": "string"}, "metrics": {"description": "Metrics to aggregate. **Supported metrics:** * `anrRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one ANR. * `anrRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `anrRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `anrRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `anrRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `userPerceivedAnrRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one user-perceived ANR. User-perceived ANRs are currently those of 'Input dispatching' type. * `userPerceivedAnrRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedAnrRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `userPerceivedAnrRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedAnrRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. Not . supported in HOURLY granularity. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `anrRate` and `userPerceivedAnrRate` metrics. A user is counted in this metric if they used the app in the foreground during the aggregation period. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value.", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Maximum size of the returned data. If unspecified, at most 1000 rows will be returned. The maximum value is 100,000; values above 100,000 will be coerced to 100,000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Specification of the timeline aggregation parameters. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the default and only supported timezone is `America/Los_Angeles`. * HOURLY: metrics are aggregated in hourly intervals. The default and only supported timezone is `UTC`."}, "userCohort": {"description": "User view to select. The output data will correspond to the selected view. **Supported values:** * `OS_PUBLIC` To select data from all publicly released Android versions. This is the default. Supports all the above dimensions. * `APP_TESTERS` To select data from users who have opted in to be testers. Supports all the above dimensions. * `OS_BETA` To select data from beta android versions only, excluding data from released android versions. Only the following dimensions are supported: * `versionCode` (int64): version of the app that was running on the user's device. * `osBuild` (string): OS build of the user's device, e.g., \"T1B2.220916.004\".", "enum": ["USER_COHORT_UNSPECIFIED", "OS_PUBLIC", "OS_BETA", "APP_TESTERS"], "enumDescriptions": ["Unspecified User cohort. This will automatically choose the default value.", "This is default view. Contains data from public released android versions only.", "This is the view with just android beta data excluding released OS version data.", "This is the view with data only from users who have opted in to be testers for a given app, excluding OS beta data."], "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryAnrRateMetricSetResponse": {"description": "Response message for QueryAnrRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryAnrRateMetricSetResponse", "properties": {"nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}, "rows": {"description": "Returned rows of data.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricsRow"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryCrashRateMetricSetRequest": {"description": "Request message for QueryCrashRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryCrashRateMetricSetRequest", "properties": {"dimensions": {"description": "Dimensions to slice the metrics by. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi.", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "Filters to apply to data. The filtering expression follows [AIP-160](https://google.aip.dev/160) standard and supports filtering by equality of all breakdown dimensions.", "type": "string"}, "metrics": {"description": "Metrics to aggregate. **Supported metrics:** * `crashRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one crash. * `crashRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `crashRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `crashRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `crashRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `userPerceivedCrashRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one crash while they were actively using your app (a user-perceived crash). An app is considered to be in active use if it is displaying any activity or executing any foreground service. * `userPerceivedCrashRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedCrashRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `userPerceivedCrashRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedCrashRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. Not supported in HOURLY granularity. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `crashRate` and `userPerceivedCrashRate` metrics. A user is counted in this metric if they used the app actively during the aggregation period. An app is considered to be in active use if it is displaying any activity or executing any foreground service. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value.", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Maximum size of the returned data. If unspecified, at most 1000 rows will be returned. The maximum value is 100,000; values above 100,000 will be coerced to 100,000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Specification of the timeline aggregation parameters. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the default and only supported timezone is `America/Los_Angeles`. * HOURLY: metrics are aggregated in hourly intervals. The default and only supported timezone is `UTC`."}, "userCohort": {"description": "User view to select. The output data will correspond to the selected view. **Supported values:** * `OS_PUBLIC` To select data from all publicly released Android versions. This is the default. Supports all the above dimensions. * `APP_TESTERS` To select data from users who have opted in to be testers. Supports all the above dimensions. * `OS_BETA` To select data from beta android versions only, excluding data from released android versions. Only the following dimensions are supported: * `versionCode` (int64): version of the app that was running on the user's device. * `osBuild` (string): OS build of the user's device, e.g., \"T1B2.220916.004\".", "enum": ["USER_COHORT_UNSPECIFIED", "OS_PUBLIC", "OS_BETA", "APP_TESTERS"], "enumDescriptions": ["Unspecified User cohort. This will automatically choose the default value.", "This is default view. Contains data from public released android versions only.", "This is the view with just android beta data excluding released OS version data.", "This is the view with data only from users who have opted in to be testers for a given app, excluding OS beta data."], "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryCrashRateMetricSetResponse": {"description": "Response message for QueryCrashRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryCrashRateMetricSetResponse", "properties": {"nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}, "rows": {"description": "Returned rows of data.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricsRow"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryErrorCountMetricSetRequest": {"description": "Request message for QueryErrorCountMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryErrorCountMetricSetRequest", "properties": {"dimensions": {"description": "Dimensions to slice the data by. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceModel` (string): unique identifier of the user's device model. * `deviceType` (string): identifier of the device's form factor, e.g., PHONE. * `reportType` (string): the type of error. The value should correspond to one of the possible values in ErrorType. * `issueId` (string): the id an error was assigned to. The value should correspond to the `{issue}` component of the issue name. * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi.", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "Filters to apply to data. The filtering expression follows [AIP-160](https://google.aip.dev/160) standard and supports filtering by equality of all breakdown dimensions and: * `isUserPerceived` (string): denotes whether error is user perceived or not, USER_PERCEIVED or NOT_USER_PERCEIVED.", "type": "string"}, "metrics": {"description": "Metrics to aggregate. **Supported metrics:** * `errorReportCount` (`google.type.Decimal`): Absolute count of individual error reports that have been received for an app. * `distinctUsers` (`google.type.Decimal`): Count of distinct users for which reports have been received. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. This value is not rounded, however it may be an approximation.", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Maximum size of the returned data. If unspecified, at most 1000 rows will be returned. The maximum value is 100000; values above 100000 will be coerced to 100000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Specification of the timeline aggregation parameters. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. The default and only supported timezone is `America/Los_Angeles`."}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryErrorCountMetricSetResponse": {"description": "Error counts query response.", "id": "GooglePlayDeveloperReportingV1beta1QueryErrorCountMetricSetResponse", "properties": {"nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}, "rows": {"description": "Returned rows.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricsRow"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryExcessiveWakeupRateMetricSetRequest": {"description": "Request message for QueryExcessiveWakeupRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryExcessiveWakeupRateMetricSetRequest", "properties": {"dimensions": {"description": "Dimensions to slice the data by. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi.", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "Filters to apply to data. The filtering expression follows [AIP-160](https://google.aip.dev/160) standard and supports filtering by equality of all breakdown dimensions.", "type": "string"}, "metrics": {"description": "Metrics to aggregate. **Supported metrics:** * `excessiveWakeupRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had more than 10 wakeups per hour. * `excessiveWakeupRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `excessiveWakeupRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `excessiveWakeupRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `excessiveWakeupRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `excessiveWakeupRate` metric. A user is counted in this metric if they app was doing any work on the device, i.e., not just active foreground usage but also background work. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value.", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Maximum size of the returned data. If unspecified, at most 1000 rows will be returned. The maximum value is 100000; values above 100000 will be coerced to 100000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Specification of the timeline aggregation parameters. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`."}, "userCohort": {"description": "User view to select. The output data will correspond to the selected view. The only supported value is `OS_PUBLIC`.", "enum": ["USER_COHORT_UNSPECIFIED", "OS_PUBLIC", "OS_BETA", "APP_TESTERS"], "enumDescriptions": ["Unspecified User cohort. This will automatically choose the default value.", "This is default view. Contains data from public released android versions only.", "This is the view with just android beta data excluding released OS version data.", "This is the view with data only from users who have opted in to be testers for a given app, excluding OS beta data."], "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryExcessiveWakeupRateMetricSetResponse": {"description": "Response message for QueryExcessiveWakeupRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryExcessiveWakeupRateMetricSetResponse", "properties": {"nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}, "rows": {"description": "Returned rows of data.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricsRow"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryLmkRateMetricSetRequest": {"description": "Request message for QueryLmkRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryLmkRateMetricSetRequest", "properties": {"dimensions": {"description": "Optional. Dimensions to slice the metrics by. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi.", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "Optional. Filters to apply to data. The filtering expression follows [AIP-160](https://google.aip.dev/160) standard and supports filtering by equality of all breakdown dimensions.", "type": "string"}, "metrics": {"description": "Optional. Metrics to aggregate. **Supported metrics:** * `userPerceivedLmkRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that experienced at least one LMK while they were actively using your app (a user-perceived LMK). An app is considered to be in active use if it is displaying any activity or executing any foreground service. * `userPerceivedLmkRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedLmkRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `userPerceivedLmkRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `userPerceivedLmkRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `userPerceivedLmkRate` metrics. A user is counted in this metric if they used the app in the foreground during the aggregation period. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value.", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Optional. Maximum size of the returned data. If unspecified, at most 1000 rows will be returned. The maximum value is 100,000; values above 100,000 will be coerced to 100,000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Optional. Specification of the timeline aggregation parameters. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the default and only supported timezone is `America/Los_Angeles`."}, "userCohort": {"description": "Optional. User view to select. The output data will correspond to the selected view. **Supported values:** * `OS_PUBLIC` To select data from all publicly released Android versions. This is the default. Supports all the above dimensions. * `APP_TESTERS` To select data from users who have opted in to be testers. Supports all the above dimensions. * `OS_BETA` To select data from beta android versions only, excluding data from released android versions. Only the following dimensions are supported: * `versionCode` (int64): version of the app that was running on the user's device. * `osBuild` (string): OS build of the user's device, e.g., \"T1B2.220916.004\".", "enum": ["USER_COHORT_UNSPECIFIED", "OS_PUBLIC", "OS_BETA", "APP_TESTERS"], "enumDescriptions": ["Unspecified User cohort. This will automatically choose the default value.", "This is default view. Contains data from public released android versions only.", "This is the view with just android beta data excluding released OS version data.", "This is the view with data only from users who have opted in to be testers for a given app, excluding OS beta data."], "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryLmkRateMetricSetResponse": {"description": "Response message for QueryLmkRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryLmkRateMetricSetResponse", "properties": {"nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}, "rows": {"description": "Returned rows of data.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricsRow"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QuerySlowRenderingRateMetricSetRequest": {"description": "Request message for QuerySlowRenderingRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QuerySlowRenderingRateMetricSetRequest", "properties": {"dimensions": {"description": "Dimensions to slice the data by. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi.", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "Filters to apply to data. The filtering expression follows [AIP-160](https://google.aip.dev/160) standard and supports filtering by equality of all breakdown dimensions.", "type": "string"}, "metrics": {"description": "Metrics to aggregate. **Supported metrics:** * `slowRenderingRate20Fps` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had a slow rendering. * `slowRenderingRate20Fps7dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowRenderingRate20Fps` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `slowRenderingRate20Fps28dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowRenderingRate20Fps` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `slowRenderingRate30Fps` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had a slow rendering. * `slowRenderingRate30Fps7dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowRenderingRate30Fps` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `slowRenderingRate30Fps28dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowRenderingRate30Fps` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `slowRenderingRate20Fps`/`slowRenderingRate30Fps` metric. A user is counted in this metric if their app was launched in the device. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value.", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Maximum size of the returned data. If unspecified, at most 1000 rows will be returned. The maximum value is 100000; values above 100000 will be coerced to 100000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Specification of the timeline aggregation parameters. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`."}, "userCohort": {"description": "User view to select. The output data will correspond to the selected view. The only supported value is `OS_PUBLIC`.", "enum": ["USER_COHORT_UNSPECIFIED", "OS_PUBLIC", "OS_BETA", "APP_TESTERS"], "enumDescriptions": ["Unspecified User cohort. This will automatically choose the default value.", "This is default view. Contains data from public released android versions only.", "This is the view with just android beta data excluding released OS version data.", "This is the view with data only from users who have opted in to be testers for a given app, excluding OS beta data."], "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QuerySlowRenderingRateMetricSetResponse": {"description": "Response message for QuerySlowRenderingRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QuerySlowRenderingRateMetricSetResponse", "properties": {"nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}, "rows": {"description": "Returned rows of data.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricsRow"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QuerySlowStartRateMetricSetRequest": {"description": "Request message for QuerySlowStartRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QuerySlowStartRateMetricSetRequest", "properties": {"dimensions": {"description": "Dimensions to slice the data by. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi.", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "Filters to apply to data. The filtering expression follows [AIP-160](https://google.aip.dev/160) standard and supports filtering by equality of all breakdown dimensions.", "type": "string"}, "metrics": {"description": "Metrics to aggregate. **Supported metrics:** * `slowStartRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had a slow start. * `slowStartRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowStartRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `slowStartRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowStartRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `slowStartRate` metric. A user is counted in this metric if their app was launched in the device. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value.", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Maximum size of the returned data. If unspecified, at most 1000 rows will be returned. The maximum value is 100000; values above 100000 will be coerced to 100000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Specification of the timeline aggregation parameters. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`."}, "userCohort": {"description": "User view to select. The output data will correspond to the selected view. The only supported value is `OS_PUBLIC`.", "enum": ["USER_COHORT_UNSPECIFIED", "OS_PUBLIC", "OS_BETA", "APP_TESTERS"], "enumDescriptions": ["Unspecified User cohort. This will automatically choose the default value.", "This is default view. Contains data from public released android versions only.", "This is the view with just android beta data excluding released OS version data.", "This is the view with data only from users who have opted in to be testers for a given app, excluding OS beta data."], "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QuerySlowStartRateMetricSetResponse": {"description": "Response message for QuerySlowStartRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QuerySlowStartRateMetricSetResponse", "properties": {"nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}, "rows": {"description": "Returned rows of data.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricsRow"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryStuckBackgroundWakelockRateMetricSetRequest": {"description": "Request message for QueryStuckBackgroundWakelockRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryStuckBackgroundWakelockRateMetricSetRequest", "properties": {"dimensions": {"description": "Dimensions to slice the data by. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi.", "items": {"type": "string"}, "type": "array"}, "filter": {"description": "Filters to apply to data. The filtering expression follows [AIP-160](https://google.aip.dev/160) standard and supports filtering by equality of all breakdown dimensions.", "type": "string"}, "metrics": {"description": "Metrics to aggregate. **Supported metrics:** * `stuckBgWakelockRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had a wakelock held in the background for longer than 1 hour. * `stuckBgWakelockRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `stuckBgWakelockRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `stuckBgWakelockRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `stuckBgWakelockRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `stuckBgWakelockRate` metric. A user is counted in this metric if they app was doing any work on the device, i.e., not just active foreground usage but also background work. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value.", "items": {"type": "string"}, "type": "array"}, "pageSize": {"description": "Maximum size of the returned data. If unspecified, at most 1000 rows will be returned. The maximum value is 100000; values above 100000 will be coerced to 100000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token, received from a previous call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to the request must match the call that provided the page token.", "type": "string"}, "timelineSpec": {"$ref": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "description": "Specification of the timeline aggregation parameters. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`."}, "userCohort": {"description": "User view to select. The output data will correspond to the selected view. The only supported value is `OS_PUBLIC`.", "enum": ["USER_COHORT_UNSPECIFIED", "OS_PUBLIC", "OS_BETA", "APP_TESTERS"], "enumDescriptions": ["Unspecified User cohort. This will automatically choose the default value.", "This is default view. Contains data from public released android versions only.", "This is the view with just android beta data excluding released OS version data.", "This is the view with data only from users who have opted in to be testers for a given app, excluding OS beta data."], "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1QueryStuckBackgroundWakelockRateMetricSetResponse": {"description": "Response message for QueryStuckBackgroundWakelockRateMetricSet.", "id": "GooglePlayDeveloperReportingV1beta1QueryStuckBackgroundWakelockRateMetricSetResponse", "properties": {"nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}, "rows": {"description": "Returned rows of data.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1MetricsRow"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1Release": {"description": "A representation of an app release.", "id": "GooglePlayDeveloperReportingV1beta1Release", "properties": {"displayName": {"description": "Readable identifier of the release.", "type": "string"}, "versionCodes": {"description": "The version codes contained in this release.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1ReleaseFilterOptions": {"description": "A set of filtering options for releases and version codes specific to an app.", "id": "GooglePlayDeveloperReportingV1beta1ReleaseFilterOptions", "properties": {"tracks": {"description": "List of tracks to filter releases over. Provides the grouping of version codes under releases and tracks.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1Track"}, "type": "array"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1SearchAccessibleAppsResponse": {"description": "Response message for SearchAccessibleApps.", "id": "GooglePlayDeveloperReportingV1beta1SearchAccessibleAppsResponse", "properties": {"apps": {"description": "The apps accessible to the user calling the endpoint.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1App"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1SearchErrorIssuesResponse": {"description": "Response with a paginated list of issues that matched the request.", "id": "GooglePlayDeveloperReportingV1beta1SearchErrorIssuesResponse", "properties": {"errorIssues": {"description": "ErrorIssues that were found.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1ErrorIssue"}, "type": "array"}, "nextPageToken": {"description": "Continuation token to fetch the next page of data.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1SearchErrorReportsResponse": {"description": "Response with a paginated list of error reports matching the search query.", "id": "GooglePlayDeveloperReportingV1beta1SearchErrorReportsResponse", "properties": {"errorReports": {"description": "Error reports that were found.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1ErrorReport"}, "type": "array"}, "nextPageToken": {"description": "Page token to fetch the next page of reports.", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1SlowRenderingRateMetricSet": {"description": "Singleton resource representing the set of Slow Rendering metrics. This metric set contains low-level rendering data captured by SurafeFlinger. Sessions are evaluated based on the present-to-present histogram of frames handled by any SurfaceFlinger layer owned by the app. A slow session is a session where more than 25% of frames for the session did not meet the metric's target frame rate (either 20fps, or 30fps). *NOTE:* This metric set is only available for games. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`. **Supported metrics:** * `slowRenderingRate20Fps` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had slow rendering. * `slowRenderingRate20Fps7dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowRenderingRate20Fps` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `slowRenderingRate20Fps28dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowRenderingRate20Fps` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `slowRenderingRate30Fps` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had slow rendering. * `slowRenderingRate30Fps7dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowRenderingRate30Fps` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `slowRenderingRate30Fps28dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowRenderingRate30Fps` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `slowRenderingRate20Fps`/`slowRenderingRate30Fps` metric. A user is counted in this metric if their app rendered any frames. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app.", "id": "GooglePlayDeveloperReportingV1beta1SlowRenderingRateMetricSet", "properties": {"freshnessInfo": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "description": "Summary about data freshness in this resource."}, "name": {"description": "Identifier. The resource name. Format: apps/{app}/slowRenderingRateMetricSet", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1SlowStartRateMetricSet": {"description": "Singleton resource representing the set of Slow Start metrics. This metric set contains Activity start duration data. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`. **Supported metrics:** * `slowStartRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had a slow start. * `slowStartRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowStartRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `slowStartRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `slowStartRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `slowStartRate` metric. A user is counted in this metric if their app was launched in the device. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value. **Required dimension:** This dimension must be specified with each request for the request to be valid. * `startType` (string): the type of start that was measured. Valid types are `HOT`, `WARM` and `COLD`. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app.", "id": "GooglePlayDeveloperReportingV1beta1SlowStartRateMetricSet", "properties": {"freshnessInfo": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "description": "Summary about data freshness in this resource."}, "name": {"description": "Identifier. The resource name. Format: apps/{app}/slowStartRateMetricSet", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1StuckBackgroundWakelockRateMetricSet": {"description": "Singleton resource representing the set of Stuck Background Wakelocks metrics. This metric set contains PowerManager wakelock duration data combined with process state data to produce a normalized metric independent of user counts. **Supported aggregation periods:** * DAILY: metrics are aggregated in calendar date intervals. Due to historical constraints, the only supported timezone is `America/Los_Angeles`. **Supported metrics:** * `stuckBgWakelockRate` (`google.type.Decimal`): Percentage of distinct users in the aggregation period that had a wakelock held in the background for longer than 1 hour. * `stuckBgWakelockRate7dUserWeighted` (`google.type.Decimal`): Rolling average value of `stuckBgWakelockRate` in the last 7 days. The daily values are weighted by the count of distinct users for the day. * `stuckBgWakelockRate28dUserWeighted` (`google.type.Decimal`): Rolling average value of `stuckBgWakelockRate` in the last 28 days. The daily values are weighted by the count of distinct users for the day. * `distinctUsers` (`google.type.Decimal`): Count of distinct users in the aggregation period that were used as normalization value for the `stuckBgWakelockRate` metric. A user is counted in this metric if their app was doing any work on the device, i.e., not just active foreground usage but also background work. Care must be taken not to aggregate this count further, as it may result in users being counted multiple times. The value is rounded to the nearest multiple of 10, 100, 1,000 or 1,000,000, depending on the magnitude of the value. **Supported dimensions:** * `apiLevel` (string): the API level of Android that was running on the user's device, e.g., 26. * `versionCode` (int64): version of the app that was running on the user's device. * `deviceModel` (string): unique identifier of the user's device model. The form of the identifier is 'deviceBrand/device', where deviceBrand corresponds to Build.BRAND and device corresponds to Build.DEVICE, e.g., google/coral. * `deviceBrand` (string): unique identifier of the user's device brand, e.g., google. * `deviceType` (string): the type (also known as form factor) of the user's device, e.g., PHONE. * `countryCode` (string): the country or region of the user's device based on their IP address, represented as a 2-letter ISO-3166 code (e.g. US for the United States). * `deviceRamBucket` (int64): RAM of the device, in MB, in buckets (3GB, 4GB, etc.). * `deviceSocMake` (string): Make of the device's primary system-on-chip, e.g., Samsung. [Reference](https://developer.android.com/reference/android/os/Build#SOC_MANUFACTURER) * `deviceSocModel` (string): Model of the device's primary system-on-chip, e.g., \"Exynos 2100\". [Reference](https://developer.android.com/reference/android/os/Build#SOC_MODEL) * `deviceCpuMake` (string): Make of the device's CPU, e.g., Qualcomm. * `deviceCpuModel` (string): Model of the device's CPU, e.g., \"Kryo 240\". * `deviceGpuMake` (string): Make of the device's GPU, e.g., ARM. * `deviceGpuModel` (string): Model of the device's GPU, e.g., Mali. * `deviceGpuVersion` (string): Version of the device's GPU, e.g., T750. * `deviceVulkanVersion` (string): Vulkan version of the device, e.g., \"4198400\". * `deviceGlEsVersion` (string): OpenGL ES version of the device, e.g., \"196610\". * `deviceScreenSize` (string): Screen size of the device, e.g., NORMAL, LARGE. * `deviceScreenDpi` (string): Screen density of the device, e.g., mdpi, hdpi. **Required permissions**: to access this resource, the calling user needs the _View app information (read-only)_ permission for the app.", "id": "GooglePlayDeveloperReportingV1beta1StuckBackgroundWakelockRateMetricSet", "properties": {"freshnessInfo": {"$ref": "GooglePlayDeveloperReportingV1beta1FreshnessInfo", "description": "Summary about data freshness in this resource."}, "name": {"description": "Identifier. The resource name. Format: apps/{app}/stuckBackgroundWakelockRateMetricSet", "type": "string"}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1TimelineSpec": {"description": "Specification of the time-related aggregation parameters of a timeline. Timelines have an aggregation period (`DAILY`, `HOURLY`, etc) which defines how events are aggregated in metrics. The points in a timeline are defined by the starting DateTime of the aggregation period. The duration is implicit in the AggregationPeriod. Hourly aggregation periods, when supported by a metric set, are always specified in UTC to avoid ambiguities around daylight saving time transitions, where an hour is skipped when adopting DST, and repeated when abandoning DST. For example, the timestamp '2021-11-07 01:00:00 America/Los_Angeles' is ambiguous since it can correspond to '2021-11-07 08:00:00 UTC' or '2021-11-07 09:00:00 UTC'. Daily aggregation periods require specifying a timezone which will determine the precise instants of the start and the end of the day. Not all metric sets support all timezones, so make sure to check which timezones are supported by the metric set you want to query.", "id": "GooglePlayDeveloperReportingV1beta1TimelineSpec", "properties": {"aggregationPeriod": {"description": "Type of the aggregation period of the datapoints in the timeline. Intervals are identified by the date and time at the start of the interval.", "enum": ["AGGREGATION_PERIOD_UNSPECIFIED", "HOURLY", "DAILY", "FULL_RANGE"], "enumDescriptions": ["Unspecified granularity.", "Data is aggregated in hourly intervals.", "Data is aggregated in daily intervals.", "Data is aggregated over the full timeline range. Effectively this produces a single value rather than a timeline."], "type": "string"}, "endTime": {"$ref": "GoogleTypeDateTime", "description": "Ending datapoint of the timeline (exclusive). See start_time for restrictions. The timezone of the end point must match the timezone of the start point."}, "startTime": {"$ref": "GoogleTypeDateTime", "description": "Starting datapoint of the timeline (inclusive). Must be aligned to the aggregation period as follows: * HOURLY: the 'minutes', 'seconds' and 'nanos' fields must be unset. The time_zone can be left unset (defaults to UTC) or set explicitly to \"UTC\". Setting any other utc_offset or timezone id will result in a validation error. * DAILY: the 'hours', 'minutes', 'seconds' and 'nanos' fields must be unset. Different metric sets support different timezones. It can be left unset to use the default timezone specified by the metric set. The timezone of the end point must match the timezone of the start point."}}, "type": "object"}, "GooglePlayDeveloperReportingV1beta1Track": {"description": "A representation of a Play release track.", "id": "GooglePlayDeveloperReportingV1beta1Track", "properties": {"displayName": {"description": "Readable identifier of the track.", "type": "string"}, "servingReleases": {"description": "Represents all active releases in the track.", "items": {"$ref": "GooglePlayDeveloperReportingV1beta1Release"}, "type": "array"}, "type": {"description": "The type of the track.", "type": "string"}}, "type": "object"}, "GoogleTypeDateTime": {"description": "Represents civil time (or occasionally physical time). This type can represent a civil time in one of a few possible ways: * When utc_offset is set and time_zone is unset: a civil time on a calendar day with a particular offset from UTC. * When time_zone is set and utc_offset is unset: a civil time on a calendar day in a particular time zone. * When neither time_zone nor utc_offset is set: a civil time on a calendar day in local time. The date is relative to the Proleptic Gregorian Calendar. If year, month, or day are 0, the DateTime is considered not to have a specific year, month, or day respectively. This type may also be used to represent a physical time if all the date and time fields are set and either case of the `time_offset` oneof is set. Consider using `Timestamp` message for physical time instead. If your use case also would like to store the user's timezone, that can be done in another field. This type is more flexible than some applications may want. Make sure to document and validate your application's limitations.", "id": "GoogleTypeDateTime", "properties": {"day": {"description": "Optional. Day of month. Must be from 1 to 31 and valid for the year and month, or 0 if specifying a datetime without a day.", "format": "int32", "type": "integer"}, "hours": {"description": "Optional. Hours of day in 24 hour format. Should be from 0 to 23, defaults to 0 (midnight). An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Optional. Minutes of hour of day. Must be from 0 to 59, defaults to 0.", "format": "int32", "type": "integer"}, "month": {"description": "Optional. Month of year. Must be from 1 to 12, or 0 if specifying a datetime without a month.", "format": "int32", "type": "integer"}, "nanos": {"description": "Optional. Fractions of seconds in nanoseconds. Must be from 0 to 999,999,999, defaults to 0.", "format": "int32", "type": "integer"}, "seconds": {"description": "Optional. Seconds of minutes of the time. Must normally be from 0 to 59, defaults to 0. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}, "timeZone": {"$ref": "GoogleTypeTimeZone", "description": "Time zone."}, "utcOffset": {"description": "UTC offset. Must be whole seconds, between -18 hours and +18 hours. For example, a UTC offset of -4:00 would be represented as { seconds: -14400 }.", "format": "google-duration", "type": "string"}, "year": {"description": "Optional. Year of date. Must be from 1 to 9999, or 0 if specifying a datetime without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleTypeDecimal": {"description": "A representation of a decimal value, such as 2.5. Clients may convert values into language-native decimal formats, such as Java's [BigDecimal](https://docs.oracle.com/en/java/javase/11/docs/api/java.base/java/math/BigDecimal.html) or Python's [decimal.Decimal](https://docs.python.org/3/library/decimal.html).", "id": "GoogleTypeDecimal", "properties": {"value": {"description": "The decimal value, as a string. The string representation consists of an optional sign, `+` (`U+002B`) or `-` (`U+002D`), followed by a sequence of zero or more decimal digits (\"the integer\"), optionally followed by a fraction, optionally followed by an exponent. An empty string **should** be interpreted as `0`. The fraction consists of a decimal point followed by zero or more decimal digits. The string must contain at least one digit in either the integer or the fraction. The number formed by the sign, the integer and the fraction is referred to as the significand. The exponent consists of the character `e` (`U+0065`) or `E` (`U+0045`) followed by one or more decimal digits. Services **should** normalize decimal values before storing them by: - Removing an explicitly-provided `+` sign (`+2.5` -> `2.5`). - Replacing a zero-length integer value with `0` (`.5` -> `0.5`). - Coercing the exponent character to upper-case, with explicit sign (`2.5e8` -> `2.5E+8`). - Removing an explicitly-provided zero exponent (`2.5E0` -> `2.5`). Services **may** perform additional normalization based on its own needs and the internal decimal implementation selected, such as shifting the decimal point and exponent value together (example: `2.5E-1` <-> `0.25`). Additionally, services **may** preserve trailing zeroes in the fraction to indicate increased precision, but are not required to do so. Note that only the `.` character is supported to divide the integer and the fraction; `,` **should not** be supported regardless of locale. Additionally, thousand separators **should not** be supported. If a service does support them, values **must** be normalized. The ENBF grammar is: DecimalString = '' | [Sign] Significand [Exponent]; Sign = '+' | '-'; Significand = Digits '.' | [Digits] '.' Digits; Exponent = ('e' | 'E') [Sign] Digits; Digits = { '0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' }; Services **should** clearly document the range of supported values, the maximum supported precision (total number of digits), and, if applicable, the scale (number of digits after the decimal point), as well as how it behaves when receiving out-of-bounds values. Services **may** choose to accept values passed as input even when the value has a higher precision or scale than the service supports, and **should** round the value to fit the supported scale. Alternatively, the service **may** error with `400 Bad Request` (`INVALID_ARGUMENT` in gRPC) if precision would be lost. Services **should** error with `400 Bad Request` (`INVALID_ARGUMENT` in gRPC) if the service receives a value outside of the supported range.", "type": "string"}}, "type": "object"}, "GoogleTypeTimeZone": {"description": "Represents a time zone from the [IANA Time Zone Database](https://www.iana.org/time-zones).", "id": "GoogleTypeTimeZone", "properties": {"id": {"description": "IANA Time Zone Database time zone. For example \"America/New_York\".", "type": "string"}, "version": {"description": "Optional. IANA Time Zone Database version number. For example \"2019a\".", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Google Play Developer Reporting API", "version": "v1beta1", "version_module": true}