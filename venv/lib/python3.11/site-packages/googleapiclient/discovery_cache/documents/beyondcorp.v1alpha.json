{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://beyondcorp.googleapis.com/", "batchPath": "batch", "canonicalName": "BeyondCorp", "description": "Beyondcorp Enterprise provides identity and context aware access controls for enterprise resources and enables zero-trust access. Using the Beyondcorp Enterprise APIs, enterprises can set up multi-cloud and on-prem connectivity solutions.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "beyondcorp:v1alpha", "kind": "discovery#restDescription", "mtlsRootUrl": "https://beyondcorp.mtls.googleapis.com/", "name": "beyondcorp", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"organizations": {"resources": {"locations": {"resources": {"global": {"resources": {"partnerTenants": {"methods": {"delete": {"deprecated": true, "description": "Deletes a single Partner<PERSON>enant.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}", "httpMethod": "DELETE", "id": "beyondcorp.organizations.locations.global.partnerTenants.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/global/partnerTenants/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Partner<PERSON><PERSON><PERSON>.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/global/partnerTenants/{partnerTenantsId}", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.global.partnerTenants.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the PartnerTenant using the form: `organizations/{organization_id}/locations/global/partnerTenants/{partner_tenant_id}`", "location": "path", "pattern": "^organizations/[^/]+/locations/global/partnerTenants/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists PartnerTenants in a given organization.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/global/partnerTenants", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.global.partnerTenants.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter specifying constraints of a list operation. All fields in the PartnerTenant message are supported. For example, the following query will return the PartnerTenants with displayName \"test-tenant\" organizations/${ORG_ID}/locations/${LOCATION}/partnerTenants?filter=displayName=\"test-tenant\" Nested fields are also supported. The follow query will return PartnerTenants with internal_tenant_id \"1234\" organizations/${ORG_ID}/locations/${LOCATION}/partnerTenants?filter=partnerMetadata.internalTenantId=\"1234\" For more information, please refer to https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListPartnerTenantsResponse, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent organization to which the PartnerTenants belong. Format: `organizations/{organization_id}/locations/global`", "location": "path", "pattern": "^organizations/[^/]+/locations/global$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/partnerTenants", "response": {"$ref": "GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "insights": {"methods": {"configuredInsight": {"description": "Gets the value for a selected particular insight based on the provided filters. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/insights/{insightsId}:configuredInsight", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.insights.configuredInsight", "parameterOrder": ["insight"], "parameters": {"aggregation": {"description": "Required. Aggregation type. Available aggregation could be fetched by calling insight list and get APIs in `BASIC` view.", "enum": ["AGGREGATION_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "CUSTOM_DATE_RANGE"], "enumDescriptions": ["Unspecified.", "Insight should be aggregated at hourly level.", "Insight should be aggregated at daily level.", "Insight should be aggregated at weekly level.", "Insight should be aggregated at monthly level.", "Insight should be aggregated at the custom date range passed in as the start and end time in the request."], "location": "query", "type": "string"}, "customGrouping.fieldFilter": {"description": "Optional. Filterable parameters to be added to the grouping clause. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter=\"fieldName1=fieldVal1 AND fieldName2=fieldVal2\". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).", "location": "query", "type": "string"}, "customGrouping.groupFields": {"description": "Required. Fields to be used for grouping. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for declaring the fields to be grouped-by here.", "location": "query", "repeated": true, "type": "string"}, "endTime": {"description": "Required. Ending time for the duration for which insight is to be pulled.", "format": "google-datetime", "location": "query", "type": "string"}, "fieldFilter": {"description": "Optional. Other filterable/configurable parameters as applicable to the selected insight. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter=\"fieldName1=fieldVal1 AND fieldName2=fieldVal2\". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).", "location": "query", "type": "string"}, "group": {"description": "Optional. Group id of the available groupings for the insight. Available groupings could be fetched by calling insight list and get APIs in `BASIC` view.", "location": "query", "type": "string"}, "insight": {"description": "Required. The resource name of the insight using the form: `organizations/{organization_id}/locations/{location_id}/insights/{insight_id}` `projects/{project_id}/locations/{location_id}/insights/{insight_id}`.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/insights/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Used to fetch the page represented by the token. Fetches the first page when not set.", "location": "query", "type": "string"}, "startTime": {"description": "Required. Starting time for the duration for which insight is to be pulled.", "format": "google-datetime", "location": "query", "type": "string"}}, "path": "v1alpha/{+insight}:configuredInsight", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the value for a selected particular insight with default configuration. The default aggregation level is 'DAILY' and no grouping will be applied or default grouping if applicable. The data will be returned for recent 7 days starting the day before. The insight data size will be limited to 50 rows. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project. Setting the `view` to `BASIC` will only return the metadata for the insight.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/insights/{insightsId}", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.insights.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the insight using the form: `organizations/{organization_id}/locations/{location_id}/insights/{insight_id}` `projects/{project_id}/locations/{location_id}/insights/{insight_id}`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/insights/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Required. Metadata only or full data view.", "enum": ["INSIGHT_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["The default / unset value. The API will default to the BASIC view.", "Include basic metadata about the insight, but not the insight data. This is the default value (for both ListInsights and GetInsight).", "Include everything."], "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists for all the available insights that could be fetched from the system. Allows to filter using category. Setting the `view` to `BASIC` will let you iterate over the list of insight metadatas.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/insights", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.insights.list", "parameterOrder": ["parent"], "parameters": {"aggregation": {"description": "Optional. Aggregation type. The default is 'DAILY'.", "enum": ["AGGREGATION_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "CUSTOM_DATE_RANGE"], "enumDescriptions": ["Unspecified.", "Insight should be aggregated at hourly level.", "Insight should be aggregated at daily level.", "Insight should be aggregated at weekly level.", "Insight should be aggregated at monthly level.", "Insight should be aggregated at the custom date range passed in as the start and end time in the request."], "location": "query", "type": "string"}, "endTime": {"description": "Optional. Ending time for the duration for which insights are to be pulled. The default is the current time.", "format": "google-datetime", "location": "query", "type": "string"}, "filter": {"description": "Optional. Filter expression to restrict the insights returned. Supported filter fields: * `type` * `category` * `subCategory` Examples: * \"category = application AND type = count\" * \"category = application AND subCategory = iap\" * \"type = status\" Allowed values: * type: [count, latency, status, list] * category: [application, device, request, security] * subCategory: [iap, caa, webprotect] NOTE: Only equality based comparison is allowed. Only `AND` conjunction is allowed. NOTE: The 'AND' in the filter field needs to be in capital letters only. NOTE: Just filtering on `subCategory` is not allowed. It should be passed in with the parent `category` too. (These expressions are based on the filter language described at https://google.aip.dev/160).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results. This is currently ignored.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. NOTE: Default page size is 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of InsightMetadata using the form: `organizations/{organization_id}/locations/{location}` `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "startTime": {"description": "Optional. Starting time for the duration for which insights are to be pulled. The default is 7 days before the current time.", "format": "google-datetime", "location": "query", "type": "string"}, "view": {"description": "Required. List only metadata or full data.", "enum": ["INSIGHT_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["The default / unset value. The API will default to the BASIC view.", "Include basic metadata about the insight, but not the insight data. This is the default value (for both ListInsights and GetInsight).", "Include everything."], "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/insights", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "beyondcorp.organizations.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:cancel", "request": {"$ref": "GoogleLongrunningCancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "beyondcorp.organizations.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "subscriptions": {"methods": {"cancel": {"description": "Cancels an existing BeyondCorp Enterprise Subscription in a given organization. Location will always be global as BeyondCorp subscriptions are per organization. Returns the timestamp for when the cancellation will become effective", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions/{subscriptionsId}:cancel", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.subscriptions.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}:cancel", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse"}}, "create": {"description": "Creates a new BeyondCorp Enterprise Subscription in a given organization. Location will always be global as BeyondCorp subscriptions are per organization.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions", "httpMethod": "POST", "id": "beyondcorp.organizations.locations.subscriptions.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource name of the subscription location using the form: `organizations/{organization_id}/locations/{location}`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/subscriptions", "request": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription"}, "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription"}}, "get": {"description": "Gets details of a single Subscription.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions/{subscriptionsId}", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.subscriptions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of Subscription using the form: `organizations/{organization_id}/locations/{location}/subscriptions/{subscription_id}`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription"}}, "list": {"description": "Lists Subscriptions in a given organization and location.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.subscriptions.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListSubscriptionsRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of Subscription using the form: `organizations/{organization_id}/locations/{location}`", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/subscriptions", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse"}}, "patch": {"description": "Updates an existing BeyondCorp Enterprise Subscription in a given organization. Location will always be global as BeyondCorp subscriptions are per organization.", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions/{subscriptionsId}", "httpMethod": "PATCH", "id": "beyondcorp.organizations.locations.subscriptions.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Unique resource name of the Subscription. The name is ignored when creating a subscription.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Field mask is used to specify the fields to be overwritten in the Subscription resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. Mutable fields: seat_count.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription"}, "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription"}}, "restart": {"description": "Restarts an existing BeyondCorp Enterprise Subscription in a given organization, that is scheduled for cancellation. Location will always be global as BeyondCorp subscriptions are per organization. Returns the timestamp for when the cancellation will become effective", "flatPath": "v1alpha/organizations/{organizationsId}/locations/{locationsId}/subscriptions/{subscriptionsId}:restart", "httpMethod": "GET", "id": "beyondcorp.organizations.locations.subscriptions.restart", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^organizations/[^/]+/locations/[^/]+/subscriptions/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}:restart", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse"}}}}}}}}, "projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudLocationLocation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1alpha/projects/{projectsId}/locations", "httpMethod": "GET", "id": "beyondcorp.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. Do not use this field. It is unsupported and is ignored unless explicitly documented otherwise. This is primarily for internal usage.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/locations", "response": {"$ref": "GoogleCloudLocationListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"appConnections": {"methods": {"create": {"description": "Creates a new AppConnection in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appConnections.create", "parameterOrder": ["parent"], "parameters": {"appConnectionId": {"description": "Optional. User-settable AppConnection resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource project name of the AppConnection location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/appConnections", "request": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single AppConnection.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}", "httpMethod": "DELETE", "id": "beyondcorp.projects.locations.appConnections.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp Connector name using the form: `projects/{project_id}/locations/{location_id}/appConnections/{app_connection_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnections/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single AppConnection.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appConnections.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp AppConnection name using the form: `projects/{project_id}/locations/{location_id}/appConnections/{app_connection_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appConnections.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists AppConnections in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appConnections.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter specifying constraints of a list operation.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListAppConnectionsRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the AppConnection location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/appConnections", "response": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single AppConnection.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}", "httpMethod": "PATCH", "id": "beyondcorp.projects.locations.appConnections.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set as true, will create the resource if it is not found.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Unique resource name of the AppConnection. The name is ignored when creating a AppConnection.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnections/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from [BeyondCorp.AppConnection]: * `labels` * `display_name` * `application_endpoint` * `connectors`", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resolve": {"description": "Resolves AppConnections details for a given AppConnector. An internal method called by a connector to find AppConnections to connect to.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections:resolve", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appConnections.resolve", "parameterOrder": ["parent"], "parameters": {"appConnectorId": {"description": "Required. BeyondCorp Connector name of the connector associated with those AppConnections using the form: `projects/{project_id}/locations/{location_id}/appConnectors/{app_connector_id}`", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ResolveAppConnectionsResponse, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the AppConnection location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/appConnections:resolve", "response": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appConnections.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnections/{appConnectionsId}:testIamPermissions", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appConnections.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "appConnectors": {"methods": {"create": {"description": "Creates a new AppConnector in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appConnectors.create", "parameterOrder": ["parent"], "parameters": {"appConnectorId": {"description": "Optional. User-settable AppConnector resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource project name of the AppConnector location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/appConnectors", "request": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single AppConnector.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}", "httpMethod": "DELETE", "id": "beyondcorp.projects.locations.appConnectors.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp AppConnector name using the form: `projects/{project_id}/locations/{location_id}/appConnectors/{app_connector_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnectors/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single AppConnector.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appConnectors.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp AppConnector name using the form: `projects/{project_id}/locations/{location_id}/appConnectors/{app_connector_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appConnectors.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists AppConnectors in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appConnectors.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter specifying constraints of a list operation.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListAppConnectorsRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the AppConnector location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/appConnectors", "response": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single AppConnector.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}", "httpMethod": "PATCH", "id": "beyondcorp.projects.locations.appConnectors.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Unique resource name of the AppConnector. The name is ignored when creating a AppConnector.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnectors/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from [BeyondCorp.AppConnector]: * `labels` * `display_name`", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "reportStatus": {"description": "Report status for a given connector.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:reportStatus", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appConnectors.reportStatus", "parameterOrder": ["appConnector"], "parameters": {"appConnector": {"description": "Required. BeyondCorp Connector name using the form: `projects/{project_id}/locations/{location_id}/connectors/{connector}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+appConnector}:reportStatus", "request": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resolveInstanceConfig": {"description": "Gets instance configuration for a given AppConnector. An internal method called by a AppConnector to get its container config.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:resolveInstanceConfig", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appConnectors.resolveInstanceConfig", "parameterOrder": ["appConnector"], "parameters": {"appConnector": {"description": "Required. BeyondCorp AppConnector name using the form: `projects/{project_id}/locations/{location_id}/appConnectors/{app_connector}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+appConnector}:resolveInstanceConfig", "response": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appConnectors.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appConnectors/{appConnectorsId}:testIamPermissions", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appConnectors.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appConnectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "appGateways": {"methods": {"create": {"description": "Creates a new AppGateway in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appGateways.create", "parameterOrder": ["parent"], "parameters": {"appGatewayId": {"description": "Optional. User-settable AppGateway resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource project name of the AppGateway location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/appGateways", "request": {"$ref": "AppGateway"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single AppGateway.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}", "httpMethod": "DELETE", "id": "beyondcorp.projects.locations.appGateways.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp AppGateway name using the form: `projects/{project_id}/locations/{location_id}/appGateways/{app_gateway_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appGateways/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single AppGateway.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appGateways.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp AppGateway name using the form: `projects/{project_id}/locations/{location_id}/appGateways/{app_gateway_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "AppGateway"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appGateways.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists AppGateways in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways", "httpMethod": "GET", "id": "beyondcorp.projects.locations.appGateways.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter specifying constraints of a list operation.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListAppGatewaysRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the AppGateway location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/appGateways", "response": {"$ref": "ListAppGatewaysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appGateways.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/appGateways/{appGatewaysId}:testIamPermissions", "httpMethod": "POST", "id": "beyondcorp.projects.locations.appGateways.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/appGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "applicationDomains": {"methods": {"getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applicationDomains/{applicationDomainsId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.applicationDomains.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applicationDomains/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applicationDomains/{applicationDomainsId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.applicationDomains.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applicationDomains/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applicationDomains/{applicationDomainsId}:testIamPermissions", "httpMethod": "POST", "id": "beyondcorp.projects.locations.applicationDomains.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applicationDomains/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "applications": {"methods": {"getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.applications.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.applications.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/applications/{applicationsId}:testIamPermissions", "httpMethod": "POST", "id": "beyondcorp.projects.locations.applications.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "connections": {"methods": {"create": {"description": "Creates a new Connection in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connections", "httpMethod": "POST", "id": "beyondcorp.projects.locations.connections.create", "parameterOrder": ["parent"], "parameters": {"connectionId": {"description": "Optional. User-settable connection resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource project name of the connection location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/connections", "request": {"$ref": "Connection"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Connection.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}", "httpMethod": "DELETE", "id": "beyondcorp.projects.locations.connections.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp Connector name using the form: `projects/{project_id}/locations/{location_id}/connections/{connection_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Connection.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.connections.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp Connection name using the form: `projects/{project_id}/locations/{location_id}/connections/{connection_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Connection"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.connections.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Connections in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connections", "httpMethod": "GET", "id": "beyondcorp.projects.locations.connections.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter specifying constraints of a list operation.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListConnectionsRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the connection location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/connections", "response": {"$ref": "ListConnectionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Connection.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}", "httpMethod": "PATCH", "id": "beyondcorp.projects.locations.connections.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set as true, will create the resource if it is not found.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Unique resource name of the connection. The name is ignored when creating a connection.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from [BeyondCorp.Connection]: * `labels` * `display_name` * `application_endpoint` * `connectors`", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "request": {"$ref": "Connection"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resolve": {"description": "Resolves connections details for a given connector. An internal method called by a connector to find connections to connect to.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connections:resolve", "httpMethod": "GET", "id": "beyondcorp.projects.locations.connections.resolve", "parameterOrder": ["parent"], "parameters": {"connectorId": {"description": "Required. BeyondCorp Connector name of the connector associated with those connections using the form: `projects/{project_id}/locations/{location_id}/connectors/{connector_id}`", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ResolveConnectionsResponse, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the connection location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/connections:resolve", "response": {"$ref": "ResolveConnectionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connections/{connectionsId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.connections.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connections/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "connectors": {"methods": {"create": {"description": "Creates a new Connector in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors", "httpMethod": "POST", "id": "beyondcorp.projects.locations.connectors.create", "parameterOrder": ["parent"], "parameters": {"connectorId": {"description": "Optional. User-settable connector resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or a letter.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource project name of the connector location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+parent}/connectors", "request": {"$ref": "Connector"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single Connector.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}", "httpMethod": "DELETE", "id": "beyondcorp.projects.locations.connectors.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp Connector name using the form: `projects/{project_id}/locations/{location_id}/connectors/{connector_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Connector.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.connectors.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp Connector name using the form: `projects/{project_id}/locations/{location_id}/connectors/{connector_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Connector"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.connectors.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Connectors in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors", "httpMethod": "GET", "id": "beyondcorp.projects.locations.connectors.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter specifying constraints of a list operation.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListConnectorsRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the connector location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/connectors", "response": {"$ref": "ListConnectorsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Connector.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}", "httpMethod": "PATCH", "id": "beyondcorp.projects.locations.connectors.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Unique resource name of the connector. The name is ignored when creating a connector.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Required. Mask of fields to update. At least one path must be supplied in this field. The elements of the repeated paths field may only include these fields from [BeyondCorp.Connector]: * `labels` * `display_name`", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "request": {"$ref": "Connector"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "reportStatus": {"description": "Report status for a given connector.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:reportStatus", "httpMethod": "POST", "id": "beyondcorp.projects.locations.connectors.reportStatus", "parameterOrder": ["connector"], "parameters": {"connector": {"description": "Required. BeyondCorp Connector name using the form: `projects/{project_id}/locations/{location_id}/connectors/{connector}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+connector}:reportStatus", "request": {"$ref": "ReportStatusRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "resolveInstanceConfig": {"description": "Gets instance configuration for a given connector. An internal method called by a connector to get its container config.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:resolveInstanceConfig", "httpMethod": "GET", "id": "beyondcorp.projects.locations.connectors.resolveInstanceConfig", "parameterOrder": ["connector"], "parameters": {"connector": {"description": "Required. BeyondCorp Connector name using the form: `projects/{project_id}/locations/{location_id}/connectors/{connector}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+connector}:resolveInstanceConfig", "response": {"$ref": "ResolveInstanceConfigResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.connectors.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "insights": {"methods": {"configuredInsight": {"description": "Gets the value for a selected particular insight based on the provided filters. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/insights/{insightsId}:configuredInsight", "httpMethod": "GET", "id": "beyondcorp.projects.locations.insights.configuredInsight", "parameterOrder": ["insight"], "parameters": {"aggregation": {"description": "Required. Aggregation type. Available aggregation could be fetched by calling insight list and get APIs in `BASIC` view.", "enum": ["AGGREGATION_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "CUSTOM_DATE_RANGE"], "enumDescriptions": ["Unspecified.", "Insight should be aggregated at hourly level.", "Insight should be aggregated at daily level.", "Insight should be aggregated at weekly level.", "Insight should be aggregated at monthly level.", "Insight should be aggregated at the custom date range passed in as the start and end time in the request."], "location": "query", "type": "string"}, "customGrouping.fieldFilter": {"description": "Optional. Filterable parameters to be added to the grouping clause. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter=\"fieldName1=fieldVal1 AND fieldName2=fieldVal2\". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).", "location": "query", "type": "string"}, "customGrouping.groupFields": {"description": "Required. Fields to be used for grouping. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for declaring the fields to be grouped-by here.", "location": "query", "repeated": true, "type": "string"}, "endTime": {"description": "Required. Ending time for the duration for which insight is to be pulled.", "format": "google-datetime", "location": "query", "type": "string"}, "fieldFilter": {"description": "Optional. Other filterable/configurable parameters as applicable to the selected insight. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter=\"fieldName1=fieldVal1 AND fieldName2=fieldVal2\". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).", "location": "query", "type": "string"}, "group": {"description": "Optional. Group id of the available groupings for the insight. Available groupings could be fetched by calling insight list and get APIs in `BASIC` view.", "location": "query", "type": "string"}, "insight": {"description": "Required. The resource name of the insight using the form: `organizations/{organization_id}/locations/{location_id}/insights/{insight_id}` `projects/{project_id}/locations/{location_id}/insights/{insight_id}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/insights/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Used to fetch the page represented by the token. Fetches the first page when not set.", "location": "query", "type": "string"}, "startTime": {"description": "Required. Starting time for the duration for which insight is to be pulled.", "format": "google-datetime", "location": "query", "type": "string"}}, "path": "v1alpha/{+insight}:configuredInsight", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the value for a selected particular insight with default configuration. The default aggregation level is 'DAILY' and no grouping will be applied or default grouping if applicable. The data will be returned for recent 7 days starting the day before. The insight data size will be limited to 50 rows. Use the organization level path for fetching at org level and project level path for fetching the insight value specific to a particular project. Setting the `view` to `BASIC` will only return the metadata for the insight.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/insights/{insightsId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.insights.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the insight using the form: `organizations/{organization_id}/locations/{location_id}/insights/{insight_id}` `projects/{project_id}/locations/{location_id}/insights/{insight_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/insights/[^/]+$", "required": true, "type": "string"}, "view": {"description": "Required. Metadata only or full data view.", "enum": ["INSIGHT_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["The default / unset value. The API will default to the BASIC view.", "Include basic metadata about the insight, but not the insight data. This is the default value (for both ListInsights and GetInsight).", "Include everything."], "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists for all the available insights that could be fetched from the system. Allows to filter using category. Setting the `view` to `BASIC` will let you iterate over the list of insight metadatas.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/insights", "httpMethod": "GET", "id": "beyondcorp.projects.locations.insights.list", "parameterOrder": ["parent"], "parameters": {"aggregation": {"description": "Optional. Aggregation type. The default is 'DAILY'.", "enum": ["AGGREGATION_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "CUSTOM_DATE_RANGE"], "enumDescriptions": ["Unspecified.", "Insight should be aggregated at hourly level.", "Insight should be aggregated at daily level.", "Insight should be aggregated at weekly level.", "Insight should be aggregated at monthly level.", "Insight should be aggregated at the custom date range passed in as the start and end time in the request."], "location": "query", "type": "string"}, "endTime": {"description": "Optional. Ending time for the duration for which insights are to be pulled. The default is the current time.", "format": "google-datetime", "location": "query", "type": "string"}, "filter": {"description": "Optional. Filter expression to restrict the insights returned. Supported filter fields: * `type` * `category` * `subCategory` Examples: * \"category = application AND type = count\" * \"category = application AND subCategory = iap\" * \"type = status\" Allowed values: * type: [count, latency, status, list] * category: [application, device, request, security] * subCategory: [iap, caa, webprotect] NOTE: Only equality based comparison is allowed. Only `AND` conjunction is allowed. NOTE: The 'AND' in the filter field needs to be in capital letters only. NOTE: Just filtering on `subCategory` is not allowed. It should be passed in with the parent `category` too. (These expressions are based on the filter language described at https://google.aip.dev/160).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results. This is currently ignored.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. Requested page size. Server may return fewer items than requested. If unspecified, server will pick an appropriate default. NOTE: Default page size is 50.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A token identifying a page of results the server should return.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of InsightMetadata using the form: `organizations/{organization_id}/locations/{location}` `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "startTime": {"description": "Optional. Starting time for the duration for which insights are to be pulled. The default is 7 days before the current time.", "format": "google-datetime", "location": "query", "type": "string"}, "view": {"description": "Required. List only metadata or full data.", "enum": ["INSIGHT_VIEW_UNSPECIFIED", "BASIC", "FULL"], "enumDescriptions": ["The default / unset value. The API will default to the BASIC view.", "Include basic metadata about the insight, but not the insight data. This is the default value (for both ListInsights and GetInsight).", "Include everything."], "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/insights", "response": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "beyondcorp.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}:cancel", "request": {"$ref": "GoogleLongrunningCancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "beyondcorp.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "beyondcorp.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}/operations", "response": {"$ref": "GoogleLongrunningListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "securityGateways": {"methods": {"create": {"description": "Creates a new Security Gateway in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways", "httpMethod": "POST", "id": "beyondcorp.projects.locations.securityGateways.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The resource project name of the SecurityGateway location using the form: `projects/{project_id}/locations/{location_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request.", "location": "query", "type": "string"}, "securityGatewayId": {"description": "Optional. User-settable SecurityGateway resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or letter.", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/securityGateways", "request": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single SecurityGateway.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}", "httpMethod": "DELETE", "id": "beyondcorp.projects.locations.securityGateways.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. BeyondCorp SecurityGateway name using the form: `projects/{project_id}/locations/{location_id}/securityGateways/{security_gateway_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single SecurityGateway.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.securityGateways.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the PartnerTenant using the form: `projects/{project_id}/locations/{location_id}/securityGateway/{security_gateway_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.securityGateways.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists SecurityGateways in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways", "httpMethod": "GET", "id": "beyondcorp.projects.locations.securityGateways.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter specifying constraints of a list operation. All fields in the SecurityGateway message are supported. For example, the following query will return the SecurityGateway with displayName \"test-security-gateway\" For more information, please refer to https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListSecurityGatewayRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent location to which the resources belong. `projects/{project_id}/locations/{location_id}/`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/securityGateways", "response": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single SecurityGateway.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}", "httpMethod": "PATCH", "id": "beyondcorp.projects.locations.securityGateways.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request timed out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Mutable fields include: display_name, hubs.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.securityGateways.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}:testIamPermissions", "httpMethod": "POST", "id": "beyondcorp.projects.locations.securityGateways.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"applications": {"methods": {"create": {"description": "Creates a new Application in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}/applications", "httpMethod": "POST", "id": "beyondcorp.projects.locations.securityGateways.applications.create", "parameterOrder": ["parent"], "parameters": {"applicationId": {"description": "Optional. User-settable Application resource ID. * Must start with a letter. * Must contain between 4-63 characters from `/a-z-/`. * Must end with a number or letter.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name of the parent SecurityGateway using the form: `projects/{project_id}/locations/global/securityGateways/{security_gateway_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request.", "location": "query", "type": "string"}}, "path": "v1alpha/{+parent}/applications", "request": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single application.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}/applications/{applicationsId}", "httpMethod": "DELETE", "id": "beyondcorp.projects.locations.securityGateways.applications.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+/applications/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "location": "query", "type": "boolean"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details of a single Application.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}/applications/{applicationsId}", "httpMethod": "GET", "id": "beyondcorp.projects.locations.securityGateways.applications.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Application using the form: `projects/{project_id}/locations/global/securityGateway/{security_gateway_id}/applications/{application_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+name}", "response": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}/applications/{applicationsId}:getIamPolicy", "httpMethod": "GET", "id": "beyondcorp.projects.locations.securityGateways.applications.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:getIamPolicy", "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Applications in a given project and location.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}/applications", "httpMethod": "GET", "id": "beyondcorp.projects.locations.securityGateways.applications.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter specifying constraints of a list operation. All fields in the Application message are supported. For example, the following query will return the Application with displayName \"test-application\" For more information, please refer to https://google.aip.dev/160.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Specifies the ordering of results. See [Sorting order](https://cloud.google.com/apis/design/design_patterns#sorting_order) for more information.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of items to return. If not specified, a default value of 50 will be used by the service. Regardless of the page_size value, the response may include a partial list and a caller should only rely on response's next_page_token to determine if there are more instances left to be queried.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListApplicationsRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent location to which the resources belong. `projects/{project_id}/locations/global/securityGateways/{security_gateway_id}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+parent}/applications", "response": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the parameters of a single Application.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}/applications/{applicationsId}", "httpMethod": "PATCH", "id": "beyondcorp.projects.locations.securityGateways.applications.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Name of the resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+/applications/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request timed out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Mutable fields include: display_name.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1alpha/{+name}", "request": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}/applications/{applicationsId}:setIamPolicy", "httpMethod": "POST", "id": "beyondcorp.projects.locations.securityGateways.applications.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:setIamPolicy", "request": {"$ref": "GoogleIamV1SetIamPolicyRequest"}, "response": {"$ref": "GoogleIamV1Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1alpha/projects/{projectsId}/locations/{locationsId}/securityGateways/{securityGatewaysId}/applications/{applicationsId}:testIamPermissions", "httpMethod": "POST", "id": "beyondcorp.projects.locations.securityGateways.applications.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/securityGateways/[^/]+/applications/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha/{+resource}:testIamPermissions", "request": {"$ref": "GoogleIamV1TestIamPermissionsRequest"}, "response": {"$ref": "GoogleIamV1TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}}}, "revision": "20250818", "rootUrl": "https://beyondcorp.googleapis.com/", "schemas": {"AllocatedConnection": {"description": "Allocated connection of the AppGateway.", "id": "AllocatedConnection", "properties": {"ingressPort": {"description": "Required. The ingress port of an allocated connection", "format": "int32", "type": "integer"}, "pscUri": {"description": "Required. The PSC uri of an allocated connection", "type": "string"}}, "type": "object"}, "AppGateway": {"description": "A BeyondCorp AppGateway resource represents a BeyondCorp protected AppGateway to a remote application. It creates all the necessary GCP components needed for creating a BeyondCorp protected AppGateway. Multiple connectors can be authorised for a single AppGateway.", "id": "AppGateway", "properties": {"allocatedConnections": {"description": "Output only. A list of connections allocated for the Gateway", "items": {"$ref": "AllocatedConnection"}, "readOnly": true, "type": "array"}, "createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. An arbitrary user-provided name for the AppGateway. Cannot exceed 64 characters.", "type": "string"}, "hostType": {"description": "Required. The type of hosting used by the AppGateway.", "enum": ["HOST_TYPE_UNSPECIFIED", "GCP_REGIONAL_MIG"], "enumDescriptions": ["Default value. This value is unused.", "AppGateway hosted in a GCP regional managed instance group."], "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata.", "type": "object"}, "name": {"description": "Required. Unique resource name of the AppGateway. The name is ignored when creating an AppGateway.", "type": "string"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the AppGateway.", "enum": ["STATE_UNSPECIFIED", "CREATING", "CREATED", "UPDATING", "DELETING", "DOWN"], "enumDescriptions": ["Default value. This value is unused.", "AppGateway is being created.", "AppGateway has been created.", "AppGateway's configuration is being updated.", "AppGateway is being deleted.", "AppGateway is down and may be restored in the future. This happens when CCFE sends ProjectState = OFF."], "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of network connectivity used by the AppGateway.", "enum": ["TYPE_UNSPECIFIED", "TCP_PROXY"], "enumDescriptions": ["Default value. This value is unused.", "TCP Proxy based BeyondCorp Connection. API will default to this if unset."], "type": "string"}, "uid": {"description": "Output only. A unique identifier for the instance generated by the system.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}, "uri": {"description": "Output only. Server-defined URI for this resource.", "readOnly": true, "type": "string"}}, "type": "object"}, "AppGatewayOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "AppGatewayOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "ApplicationEndpoint": {"description": "ApplicationEndpoint represents a remote application endpoint.", "id": "ApplicationEndpoint", "properties": {"host": {"description": "Required. Hostname or IP address of the remote application endpoint.", "type": "string"}, "port": {"description": "Required. Port of the remote application endpoint.", "format": "int32", "type": "integer"}}, "type": "object"}, "CloudPubSubNotificationConfig": {"description": "The configuration for Pub/Sub messaging for the connector.", "id": "CloudPubSubNotificationConfig", "properties": {"pubsubSubscription": {"description": "The Pub/Sub subscription the connector uses to receive notifications.", "type": "string"}}, "type": "object"}, "CloudSecurityZerotrustApplinkAppConnectorProtoConnectionConfig": {"description": "ConnectionConfig represents a Connection Configuration object.", "id": "CloudSecurityZerotrustApplinkAppConnectorProtoConnectionConfig", "properties": {"applicationEndpoint": {"description": "application_endpoint is the endpoint of the application the form of host:port. For example, \"localhost:80\".", "type": "string"}, "applicationName": {"description": "application_name represents the given name of the application the connection is connecting with.", "type": "string"}, "gateway": {"description": "gateway lists all instances running a gateway in GCP. They all connect to a connector on the host.", "items": {"$ref": "CloudSecurityZerotrustApplinkAppConnectorProtoGateway"}, "type": "array"}, "name": {"description": "name is the unique ID for each connection. TODO(b/190732451) returns connection name from user-specified name in config. Now, name = ${application_name}:${application_endpoint}", "type": "string"}, "project": {"description": "project represents the consumer project the connection belongs to.", "type": "string"}, "tunnelsPerGateway": {"description": "tunnels_per_gateway reflects the number of tunnels between a connector and a gateway.", "format": "uint32", "type": "integer"}, "userPort": {"description": "user_port specifies the reserved port on gateways for user connections.", "format": "int32", "type": "integer"}}, "type": "object"}, "CloudSecurityZerotrustApplinkAppConnectorProtoConnectorDetails": {"description": "ConnectorDetails reflects the details of a connector.", "id": "CloudSecurityZerotrustApplinkAppConnectorProtoConnectorDetails", "properties": {}, "type": "object"}, "CloudSecurityZerotrustApplinkAppConnectorProtoGateway": {"description": "Gateway represents a GCE VM Instance endpoint for use by IAP TCP.", "id": "CloudSecurityZerotrustApplinkAppConnectorProtoGateway", "properties": {"interface": {"description": "interface specifies the network interface of the gateway to connect to.", "type": "string"}, "name": {"description": "name is the name of an instance running a gateway. It is the unique ID for a gateway. All gateways under the same connection have the same prefix. It is derived from the gateway URL. For example, name=${instance} assuming a gateway URL. https://www.googleapis.com/compute/${version}/projects/${project}/zones/${zone}/instances/${instance}", "type": "string"}, "port": {"description": "port specifies the port of the gateway for tunnel connections from the connectors.", "format": "uint32", "type": "integer"}, "project": {"description": "project is the tenant project the gateway belongs to. Different from the project in the connection, it is a BeyondCorpAPI internally created project to manage all the gateways. It is sharing the same network with the consumer project user owned. It is derived from the gateway URL. For example, project=${project} assuming a gateway URL. https://www.googleapis.com/compute/${version}/projects/${project}/zones/${zone}/instances/${instance}", "type": "string"}, "selfLink": {"description": "self_link is the gateway URL in the form https://www.googleapis.com/compute/${version}/projects/${project}/zones/${zone}/instances/${instance}", "type": "string"}, "zone": {"description": "zone represents the zone the instance belongs. It is derived from the gateway URL. For example, zone=${zone} assuming a gateway URL. https://www.googleapis.com/compute/${version}/projects/${project}/zones/${zone}/instances/${instance}", "type": "string"}}, "type": "object"}, "CloudSecurityZerotrustApplinkLogagentProtoLogAgentDetails": {"description": "LogAgentDetails reflects the details of a log agent.", "id": "CloudSecurityZerotrustApplinkLogagentProtoLogAgentDetails", "properties": {}, "type": "object"}, "Connection": {"description": "A BeyondCorp Connection resource represents a BeyondCorp protected connection to a remote application. It creates all the necessary GCP components needed for creating a BeyondCorp protected connection. Multiple connectors can be authorised for a single Connection.", "id": "Connection", "properties": {"applicationEndpoint": {"$ref": "ApplicationEndpoint", "description": "Required. Address of the remote application endpoint for the BeyondCorp Connection."}, "connectors": {"description": "Optional. List of [google.cloud.beyondcorp.v1main.Connector.name] that are authorised to be associated with this Connection.", "items": {"type": "string"}, "type": "array"}, "createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. An arbitrary user-provided name for the connection. Cannot exceed 64 characters.", "type": "string"}, "gateway": {"$ref": "Gateway", "description": "Optional. Gateway used by the connection."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata.", "type": "object"}, "name": {"description": "Required. Unique resource name of the connection. The name is ignored when creating a connection.", "type": "string"}, "state": {"description": "Output only. The current state of the connection.", "enum": ["STATE_UNSPECIFIED", "CREATING", "CREATED", "UPDATING", "DELETING", "DOWN"], "enumDescriptions": ["Default value. This value is unused.", "Connection is being created.", "Connection has been created.", "Connection's configuration is being updated.", "Connection is being deleted.", "Connection is down and may be restored in the future. This happens when CCFE sends ProjectState = OFF."], "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of network connectivity used by the connection.", "enum": ["TYPE_UNSPECIFIED", "TCP_PROXY"], "enumDescriptions": ["Default value. This value is unused.", "TCP Proxy based BeyondCorp Connection. API will default to this if unset."], "type": "string"}, "uid": {"description": "Output only. A unique identifier for the instance generated by the system.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ConnectionDetails": {"description": "Details of the Connection.", "id": "ConnectionDetails", "properties": {"connection": {"$ref": "Connection", "description": "A BeyondCorp Connection in the project."}, "recentMigVms": {"description": "If type=GCP_REGIONAL_MIG, contains most recent VM instances, like \"https://www.googleapis.com/compute/v1/projects/{project_id}/zones/{zone_id}/instances/{instance_id}\".", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ConnectionOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "ConnectionOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Connector": {"description": "A BeyondCorp connector resource that represents an application facing component deployed proximal to and with direct access to the application instances. It is used to establish connectivity between the remote enterprise environment and GCP. It initiates connections to the applications and can proxy the data from users over the connection.", "id": "Connector", "properties": {"createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. An arbitrary user-provided name for the connector. Cannot exceed 64 characters.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata.", "type": "object"}, "name": {"description": "Required. Unique resource name of the connector. The name is ignored when creating a connector.", "type": "string"}, "principalInfo": {"$ref": "PrincipalInfo", "description": "Required. Principal information about the Identity of the connector."}, "resourceInfo": {"$ref": "ResourceInfo", "description": "Optional. Resource info of the connector."}, "state": {"description": "Output only. The current state of the connector.", "enum": ["STATE_UNSPECIFIED", "CREATING", "CREATED", "UPDATING", "DELETING", "DOWN"], "enumDescriptions": ["Default value. This value is unused.", "Connector is being created.", "Connector has been created.", "Connector's configuration is being updated.", "Connector is being deleted.", "Connector is down and may be restored in the future. This happens when CCFE sends ProjectState = OFF."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. A unique identifier for the instance generated by the system.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ConnectorInstanceConfig": {"description": "ConnectorInstanceConfig defines the instance config of a connector.", "id": "ConnectorInstanceConfig", "properties": {"imageConfig": {"$ref": "ImageConfig", "description": "ImageConfig defines the GCR images to run for the remote agent's control plane."}, "instanceConfig": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The SLM instance agent configuration.", "type": "object"}, "notificationConfig": {"$ref": "NotificationConfig", "description": "NotificationConfig defines the notification mechanism that the remote instance should subscribe to in order to receive notification."}, "sequenceNumber": {"description": "Required. A monotonically increasing number generated and maintained by the API provider. Every time a config changes in the backend, the sequenceNumber should be bumped up to reflect the change.", "format": "int64", "type": "string"}}, "type": "object"}, "ConnectorOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "ConnectorOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "ContainerHealthDetails": {"description": "ContainerHealthDetails reflects the health details of a container.", "id": "ContainerHealthDetails", "properties": {"currentConfigVersion": {"description": "The version of the current config.", "type": "string"}, "errorMsg": {"description": "The latest error message.", "type": "string"}, "expectedConfigVersion": {"description": "The version of the expected config.", "type": "string"}, "extendedStatus": {"additionalProperties": {"type": "string"}, "description": "The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.", "type": "object"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "Gateway": {"description": "Gateway represents a user facing component that serves as an entrance to enable connectivity.", "id": "Gateway", "properties": {"type": {"description": "Required. The type of hosting used by the gateway.", "enum": ["TYPE_UNSPECIFIED", "GCP_REGIONAL_MIG"], "enumDescriptions": ["Default value. This value is unused.", "Gateway hosted in a GCP regional managed instance group."], "type": "string"}, "uri": {"description": "Output only. Server-defined URI for this resource.", "readOnly": true, "type": "string"}, "userPort": {"description": "Output only. User port reserved on the gateways for this connection, if not specified or zero, the default port is 19443.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectionsV1AppConnectionOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpAppconnectionsV1AppConnectionOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection": {"description": "A BeyondCorp AppConnection resource represents a BeyondCorp protected AppConnection to a remote application. It creates all the necessary GCP components needed for creating a BeyondCorp protected AppConnection. Multiple connectors can be authorised for a single AppConnection.", "id": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection", "properties": {"applicationEndpoint": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionApplicationEndpoint", "description": "Required. Address of the remote application endpoint for the BeyondCorp AppConnection."}, "connectors": {"description": "Optional. List of [google.cloud.beyondcorp.v1main.Connector.name] that are authorised to be associated with this AppConnection.", "items": {"type": "string"}, "type": "array"}, "createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. An arbitrary user-provided name for the AppConnection. Cannot exceed 64 characters.", "type": "string"}, "gateway": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionGateway", "description": "Optional. Gateway used by the AppConnection."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata.", "type": "object"}, "name": {"description": "Required. Unique resource name of the AppConnection. The name is ignored when creating a AppConnection.", "type": "string"}, "satisfiesPzi": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "satisfiesPzs": {"description": "Output only. Reserved for future use.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The current state of the AppConnection.", "enum": ["STATE_UNSPECIFIED", "CREATING", "CREATED", "UPDATING", "DELETING", "DOWN"], "enumDescriptions": ["Default value. This value is unused.", "AppConnection is being created.", "AppConnection has been created.", "AppConnection's configuration is being updated.", "AppConnection is being deleted.", "AppConnection is down and may be restored in the future. This happens when CCFE sends ProjectState = OFF."], "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of network connectivity used by the AppConnection.", "enum": ["TYPE_UNSPECIFIED", "TCP_PROXY"], "enumDescriptions": ["Default value. This value is unused.", "TCP Proxy based BeyondCorp AppConnection. API will default to this if unset."], "type": "string"}, "uid": {"description": "Output only. A unique identifier for the instance generated by the system.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionApplicationEndpoint": {"description": "ApplicationEndpoint represents a remote application endpoint.", "id": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionApplicationEndpoint", "properties": {"host": {"description": "Required. Hostname or IP address of the remote application endpoint.", "type": "string"}, "port": {"description": "Required. Port of the remote application endpoint.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionGateway": {"description": "Gateway represents a user facing component that serves as an entrance to enable connectivity.", "id": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionGateway", "properties": {"appGateway": {"description": "Required. AppGateway name in following format: `projects/{project_id}/locations/{location_id}/appgateways/{gateway_id}`", "type": "string"}, "ingressPort": {"description": "Output only. Ingress port reserved on the gateways for this AppConnection, if not specified or zero, the default port is 19443.", "format": "int32", "readOnly": true, "type": "integer"}, "l7psc": {"description": "Output only. L7 private service connection for this resource.", "readOnly": true, "type": "string"}, "type": {"description": "Required. The type of hosting used by the gateway.", "enum": ["TYPE_UNSPECIFIED", "GCP_REGIONAL_MIG"], "enumDescriptions": ["Default value. This value is unused.", "Gateway hosted in a GCP regional managed instance group."], "type": "string"}, "uri": {"description": "Output only. Server-defined URI for this resource.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnectionOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse": {"description": "Response message for BeyondCorp.ListAppConnections.", "id": "GoogleCloudBeyondcorpAppconnectionsV1alphaListAppConnectionsResponse", "properties": {"appConnections": {"description": "A list of BeyondCorp AppConnections in the project.", "items": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse": {"description": "Response message for BeyondCorp.ResolveAppConnections.", "id": "GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponse", "properties": {"appConnectionDetails": {"description": "A list of BeyondCorp AppConnections with details in the project.", "items": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponseAppConnectionDetails"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponseAppConnectionDetails": {"description": "Details of the AppConnection.", "id": "GoogleCloudBeyondcorpAppconnectionsV1alphaResolveAppConnectionsResponseAppConnectionDetails", "properties": {"appConnection": {"$ref": "GoogleCloudBeyondcorpAppconnectionsV1alphaAppConnection", "description": "A BeyondCorp AppConnection in the project."}, "recentMigVms": {"description": "If type=GCP_REGIONAL_MIG, contains most recent VM instances, like `https://www.googleapis.com/compute/v1/projects/{project_id}/zones/{zone_id}/instances/{instance_id}`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1AppConnectorOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpAppconnectorsV1AppConnectorOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1ContainerHealthDetails": {"description": "ContainerHealthDetails reflects the health details of a container.", "id": "GoogleCloudBeyondcorpAppconnectorsV1ContainerHealthDetails", "properties": {"currentConfigVersion": {"description": "The version of the current config.", "type": "string"}, "errorMsg": {"description": "The latest error message.", "type": "string"}, "expectedConfigVersion": {"description": "The version of the expected config.", "type": "string"}, "extendedStatus": {"additionalProperties": {"type": "string"}, "description": "The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.", "type": "object"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1RemoteAgentDetails": {"description": "RemoteAgentDetails reflects the details of a remote agent.", "id": "GoogleCloudBeyondcorpAppconnectorsV1RemoteAgentDetails", "properties": {}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector": {"description": "A BeyondCorp connector resource that represents an application facing component deployed proximal to and with direct access to the application instances. It is used to establish connectivity between the remote enterprise environment and GCP. It initiates connections to the applications and can proxy the data from users over the connection.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector", "properties": {"createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. An arbitrary user-provided name for the AppConnector. Cannot exceed 64 characters.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Resource labels to represent user provided metadata.", "type": "object"}, "name": {"description": "Required. Unique resource name of the AppConnector. The name is ignored when creating a AppConnector.", "type": "string"}, "principalInfo": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfo", "description": "Required. Principal information about the Identity of the AppConnector."}, "resourceInfo": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo", "description": "Optional. Resource info of the connector."}, "state": {"description": "Output only. The current state of the AppConnector.", "enum": ["STATE_UNSPECIFIED", "CREATING", "CREATED", "UPDATING", "DELETING", "DOWN"], "enumDescriptions": ["Default value. This value is unused.", "AppConnector is being created.", "AppConnector has been created.", "AppConnector's configuration is being updated.", "AppConnector is being deleted.", "AppConnector is down and may be restored in the future. This happens when CCFE sends ProjectState = OFF."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. A unique identifier for the instance generated by the system.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorInstanceConfig": {"description": "AppConnectorInstanceConfig defines the instance config of a AppConnector.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorInstanceConfig", "properties": {"imageConfig": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaImageConfig", "description": "ImageConfig defines the GCR images to run for the remote agent's control plane."}, "instanceConfig": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The SLM instance agent configuration.", "type": "object"}, "notificationConfig": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfig", "description": "NotificationConfig defines the notification mechanism that the remote instance should subscribe to in order to receive notification."}, "sequenceNumber": {"description": "Required. A monotonically increasing number generated and maintained by the API provider. Every time a config changes in the backend, the sequenceNumber should be bumped up to reflect the change.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfo": {"description": "PrincipalInfo represents an Identity oneof.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfo", "properties": {"serviceAccount": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfoServiceAccount", "description": "A GCP service account."}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfoServiceAccount": {"description": "ServiceAccount represents a GCP service account.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorPrincipalInfoServiceAccount", "properties": {"email": {"description": "Email address of the service account.", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaContainerHealthDetails": {"description": "ContainerHealthDetails reflects the health details of a container.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaContainerHealthDetails", "properties": {"currentConfigVersion": {"description": "The version of the current config.", "type": "string"}, "errorMsg": {"description": "The latest error message.", "type": "string"}, "expectedConfigVersion": {"description": "The version of the expected config.", "type": "string"}, "extendedStatus": {"additionalProperties": {"type": "string"}, "description": "The extended status. Such as ExitCode, StartedAt, FinishedAt, etc.", "type": "object"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaImageConfig": {"description": "ImageConfig defines the control plane images to run.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaImageConfig", "properties": {"stableImage": {"description": "The stable image that the remote agent will fallback to if the target image fails. Format would be a gcr image path, e.g.: gcr.io/PROJECT-ID/my-image:tag1", "type": "string"}, "targetImage": {"description": "The initial image the remote agent will attempt to run for the control plane. Format would be a gcr image path, e.g.: gcr.io/PROJECT-ID/my-image:tag1", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse": {"description": "Response message for BeyondCorp.ListAppConnectors.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaListAppConnectorsResponse", "properties": {"appConnectors": {"description": "A list of BeyondCorp AppConnectors in the project.", "items": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnector"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfig": {"description": "NotificationConfig defines the mechanisms to notify instance agent.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfig", "properties": {"pubsubNotification": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfigCloudPubSubNotificationConfig", "description": "Cloud Pub/Sub Configuration to receive notifications."}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfigCloudPubSubNotificationConfig": {"description": "The configuration for Pub/Sub messaging for the AppConnector.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaNotificationConfigCloudPubSubNotificationConfig", "properties": {"pubsubSubscription": {"description": "The Pub/Sub subscription the AppConnector uses to receive notifications.", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaRemoteAgentDetails": {"description": "RemoteAgentDetails reflects the details of a remote agent.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaRemoteAgentDetails", "properties": {}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest": {"description": "Request report the connector status.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaReportStatusRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "resourceInfo": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo", "description": "Required. Resource info of the connector."}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "type": "boolean"}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse": {"description": "Response message for BeyondCorp.ResolveInstanceConfig.", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaResolveInstanceConfigResponse", "properties": {"instanceConfig": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaAppConnectorInstanceConfig", "description": "AppConnectorInstanceConfig."}}, "type": "object"}, "GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo": {"description": "ResourceInfo represents the information/status of an app connector resource. Such as: - remote_agent - container - runtime - appgateway - appconnector - appconnection - tunnel - logagent", "id": "GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo", "properties": {"id": {"description": "Required. Unique Id for the resource.", "type": "string"}, "resource": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Specific details for the resource. This is for internal use only.", "type": "object"}, "status": {"description": "Overall health status. Overall status is derived based on the status of each sub level resources.", "enum": ["HEALTH_STATUS_UNSPECIFIED", "HEALTHY", "UNHEALTHY", "UNRESPONSIVE", "DEGRADED"], "enumDescriptions": ["Health status is unknown: not initialized or failed to retrieve.", "The resource is healthy.", "The resource is unhealthy.", "The resource is unresponsive.", "Some sub-resources are UNHEALTHY."], "type": "string"}, "sub": {"description": "List of Info for the sub level resources.", "items": {"$ref": "GoogleCloudBeyondcorpAppconnectorsV1alphaResourceInfo"}, "type": "array"}, "time": {"description": "The timestamp to collect the info. It is suggested to be set by the topmost level resource only.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpAppgatewaysV1AppGatewayOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpAppgatewaysV1AppGatewayOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpPartnerservicesV1alphaGroup": {"description": "Message to capture group information", "id": "GoogleCloudBeyondcorpPartnerservicesV1alphaGroup", "properties": {"email": {"description": "The group email id", "type": "string"}, "id": {"description": "Google group id", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse": {"description": "Message for response to listing PartnerTenants.", "id": "GoogleCloudBeyondcorpPartnerservicesV1alphaListPartnerTenantsResponse", "properties": {"nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "partnerTenants": {"description": "The list of PartnerTenant objects.", "items": {"$ref": "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerMetadata": {"description": "Metadata associated with PartnerTenant and is provided by the Partner.", "id": "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerMetadata", "properties": {"internalTenantId": {"description": "Optional. UUID used by the Partner to refer to the PartnerTenant in their internal systems.", "type": "string"}, "partnerTenantId": {"description": "Optional. UUID used by the Partner to refer to the PartnerTenant in their internal systems.", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerServiceOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerServiceOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the caller has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant": {"description": "Information about a BeyoncCorp Enterprise PartnerTenant.", "id": "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerTenant", "properties": {"createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. An arbitrary caller-provided name for the PartnerTenant. Cannot exceed 64 characters.", "type": "string"}, "group": {"$ref": "GoogleCloudBeyondcorpPartnerservicesV1alphaGroup", "description": "Optional. Group information for the users enabled to use the partnerTenant. If the group information is not provided then the partnerTenant will be enabled for all users."}, "name": {"description": "Output only. Unique resource name of the PartnerTenant. The name is ignored when creating PartnerTenant.", "readOnly": true, "type": "string"}, "partnerMetadata": {"$ref": "GoogleCloudBeyondcorpPartnerservicesV1alphaPartnerMetadata", "description": "Optional. Metadata provided by the Partner associated with PartnerTenant."}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpPartnerservicesV1mainPartnerServiceOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpPartnerservicesV1mainPartnerServiceOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the caller has requested cancellation of the operation. Operations that have successfully been cancelled have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig": {"description": "The configuration that was applied to generate the result.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig", "properties": {"aggregation": {"description": "Output only. Aggregation type applied.", "enum": ["AGGREGATION_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "CUSTOM_DATE_RANGE"], "enumDescriptions": ["Unspecified.", "Insight should be aggregated at hourly level.", "Insight should be aggregated at daily level.", "Insight should be aggregated at weekly level.", "Insight should be aggregated at monthly level.", "Insight should be aggregated at the custom date range passed in as the start and end time in the request."], "readOnly": true, "type": "string"}, "customGrouping": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaCustomGrouping", "description": "Output only. Customised grouping applied.", "readOnly": true}, "endTime": {"description": "Output only. Ending time for the duration for which insight was pulled.", "format": "google-datetime", "readOnly": true, "type": "string"}, "fieldFilter": {"description": "Output only. Filters applied.", "readOnly": true, "type": "string"}, "group": {"description": "Output only. Group id of the grouping applied.", "readOnly": true, "type": "string"}, "startTime": {"description": "Output only. Starting time for the duration for which insight was pulled.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse": {"description": "The response for the configured insight.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaConfiguredInsightResponse", "properties": {"appliedConfig": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig", "description": "Output only. Applied insight config to generate the result data rows.", "readOnly": true}, "nextPageToken": {"description": "Output only. Next page token to be fetched. Set to empty or NULL if there are no more pages available.", "readOnly": true, "type": "string"}, "rows": {"description": "Output only. Result rows returned containing the required value(s) for configured insight.", "items": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaCustomGrouping": {"description": "Customised grouping option that allows setting the group_by fields and also the filters togather for a configured insight request.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaCustomGrouping", "properties": {"fieldFilter": {"description": "Optional. Filterable parameters to be added to the grouping clause. Available fields could be fetched by calling insight list and get APIs in `BASIC` view. `=` is the only comparison operator supported. `AND` is the only logical operator supported. Usage: field_filter=\"fieldName1=fieldVal1 AND fieldName2=fieldVal2\". NOTE: Only `AND` conditions are allowed. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for the filtering the corresponding fields in this filter field. (These expressions are based on the filter language described at https://google.aip.dev/160).", "type": "string"}, "groupFields": {"description": "Required. Fields to be used for grouping. NOTE: Use the `filter_alias` from `Insight.Metadata.Field` message for declaring the fields to be grouped-by here.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight": {"description": "The Insight object with configuration that was returned and actual list of records.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight", "properties": {"appliedConfig": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaAppliedConfig", "description": "Output only. Applied insight config to generate the result data rows.", "readOnly": true}, "metadata": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadata", "description": "Output only. Metadata for the Insight.", "readOnly": true}, "name": {"description": "Output only. The insight resource name. e.g. `organizations/{organization_id}/locations/{location_id}/insights/{insight_id}` OR `projects/{project_id}/locations/{location_id}/insights/{insight_id}`.", "readOnly": true, "type": "string"}, "rows": {"description": "Output only. Result rows returned containing the required value(s).", "items": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadata": {"description": "Insight filters, groupings and aggregations that can be applied for the insight. Examples: aggregations, groups, field filters.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadata", "properties": {"aggregations": {"description": "Output only. List of aggregation types available for insight.", "items": {"enum": ["AGGREGATION_UNSPECIFIED", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "CUSTOM_DATE_RANGE"], "enumDescriptions": ["Unspecified.", "Insight should be aggregated at hourly level.", "Insight should be aggregated at daily level.", "Insight should be aggregated at weekly level.", "Insight should be aggregated at monthly level.", "Insight should be aggregated at the custom date range passed in as the start and end time in the request."], "type": "string"}, "readOnly": true, "type": "array"}, "category": {"description": "Output only. Category of the insight.", "readOnly": true, "type": "string"}, "displayName": {"description": "Output only. Common name of the insight.", "readOnly": true, "type": "string"}, "fields": {"description": "Output only. List of fields available for insight.", "items": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadataField"}, "readOnly": true, "type": "array"}, "groups": {"description": "Output only. List of groupings available for insight.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "subCategory": {"description": "Output only. Sub-Category of the insight.", "readOnly": true, "type": "string"}, "type": {"description": "Output only. Type of the insight. It is metadata describing whether the insight is a metric (e.g. count) or a report (e.g. list, status).", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadataField": {"description": "Field metadata. Commonly understandable name and description for the field. Multiple such fields constitute the Insight.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsightMetadataField", "properties": {"description": {"description": "Output only. Description of the field.", "readOnly": true, "type": "string"}, "displayName": {"description": "Output only. Name of the field.", "readOnly": true, "type": "string"}, "filterAlias": {"description": "Output only. Field name to be used in filter while requesting configured insight filtered on this field.", "readOnly": true, "type": "string"}, "filterable": {"description": "Output only. Indicates whether the field can be used for filtering.", "readOnly": true, "type": "boolean"}, "groupable": {"description": "Output only. Indicates whether the field can be used for grouping in custom grouping request.", "readOnly": true, "type": "boolean"}, "id": {"description": "Output only. Field id for which this is the metadata.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse": {"description": "The response for the list of insights.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaListInsightsResponse", "properties": {"insights": {"description": "Output only. List of all insights.", "items": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaInsight"}, "readOnly": true, "type": "array"}, "nextPageToken": {"description": "Output only. Next page token to be fetched. Set to empty or NULL if there are no more pages available.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow": {"description": "Row of the fetch response consisting of a set of entries.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRow", "properties": {"fieldValues": {"description": "Output only. Columns/entries/key-vals in the result.", "items": {"$ref": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRowFieldVal"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRowFieldVal": {"description": "Column or key value pair from the request as part of key to use in query or a single pair of the fetch response.", "id": "GoogleCloudBeyondcorpSaasplatformInsightsV1alphaRowFieldVal", "properties": {"displayName": {"description": "Output only. Name of the field.", "readOnly": true, "type": "string"}, "filterAlias": {"description": "Output only. Field name to be used in filter while requesting configured insight filtered on this field.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. Field id.", "readOnly": true, "type": "string"}, "value": {"description": "Output only. Value of the field in string format. Acceptable values are strings or numbers.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse": {"description": "Response message for BeyondCorp.CancelSubscription", "id": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaCancelSubscriptionResponse", "properties": {"effectiveCancellationTime": {"description": "Time when the cancellation will become effective", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse": {"description": "Response message for BeyondCorp.ListSubscriptions.", "id": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaListSubscriptionsResponse", "properties": {"nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "subscriptions": {"description": "A list of BeyondCorp Subscriptions in the organization.", "items": {"$ref": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse": {"description": "Response message for BeyondCorp.RestartSubscription", "id": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaRestartSubscriptionResponse", "properties": {}, "type": "object"}, "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription": {"description": "A BeyondCorp Subscription resource represents BeyondCorp Enterprise Subscription. BeyondCorp Enterprise Subscription enables BeyondCorp Enterprise permium features for an organization.", "id": "GoogleCloudBeyondcorpSaasplatformSubscriptionsV1alphaSubscription", "properties": {"autoRenewEnabled": {"description": "Output only. Represents that, if subscription will renew or end when the term ends.", "readOnly": true, "type": "boolean"}, "billingAccount": {"description": "Optional. Name of the billing account in the format. e.g. billingAccounts/123456-123456-123456 Required if Subscription is of Paid type.", "type": "string"}, "createTime": {"description": "Output only. Create time of the subscription.", "format": "google-datetime", "readOnly": true, "type": "string"}, "csgCustomer": {"description": "Optional. Whether the subscription is being created as part of the Citrix flow. If this field is set to true, the subscription should have both the start_time and end_time set in the request and the billing account used will be the Citrix master billing account regardless of what its set to in the request. This field can only be set to true in create requests.", "type": "boolean"}, "endTime": {"description": "Optional. End time of the subscription.", "format": "google-datetime", "type": "string"}, "name": {"description": "Identifier. Unique resource name of the Subscription. The name is ignored when creating a subscription.", "type": "string"}, "seatCount": {"description": "Optional. Number of seats in the subscription.", "format": "int64", "type": "string"}, "sku": {"description": "Required. SKU of subscription.", "enum": ["SKU_UNSPECIFIED", "BCE_STANDARD_SKU"], "enumDescriptions": ["Default value. This value is unused.", "Represents BeyondCorp Standard SKU."], "type": "string"}, "startTime": {"description": "Optional. Start time of the subscription.", "format": "google-datetime", "type": "string"}, "state": {"description": "Output only. The current state of the subscription.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "INACTIVE", "COMPLETED"], "enumDescriptions": ["Default value. This value is unused.", "Represents an active subscription.", "Represents an upcomming subscription.", "Represents a completed subscription."], "readOnly": true, "type": "string"}, "subscriberType": {"description": "Output only. Type of subscriber.", "enum": ["SUBSCRIBER_TYPE_UNSPECIFIED", "ONLINE", "OFFLINE"], "enumDescriptions": ["Default value. This value is unused.", "Represents an online subscription.", "Represents an offline subscription."], "readOnly": true, "type": "string"}, "type": {"description": "Required. Type of subscription.", "enum": ["TYPE_UNSPECIFIED", "TRIAL", "PAID", "ALLOWLIST"], "enumDescriptions": ["Default value. This value is unused.", "Represents a trial subscription.", "Represents a paid subscription.", "Reresents an allowlisted subscription."], "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1SecurityGatewayOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1SecurityGatewayOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication": {"description": "The information about an application resource.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication", "properties": {"createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. An arbitrary user-provided name for the application resource. Cannot exceed 64 characters.", "type": "string"}, "endpointMatchers": {"description": "Required. Endpoint matchers associated with an application. A combination of hostname and ports as endpoint matchers is used to match the application. Match conditions for OR logic. An array of match conditions to allow for multiple matching criteria. The rule is considered a match if one of the conditions is met. The conditions can be one of the following combinations (Hostname), (Hostname & Ports) EXAMPLES: Hostname - (\"*.example.com\"), (\"xyz.example.com\") Hostname and Ports - (\"example.com\" and \"22\"), (\"example.com\" and \"22,33\") etc", "items": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaEndpointMatcher"}, "type": "array"}, "name": {"description": "Identifier. Name of the resource.", "type": "string"}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}, "upstreams": {"description": "Optional. Which upstream resources to forward traffic to.", "items": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplicationUpstream"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplicationUpstream": {"description": "Which upstream resource to forward traffic to.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplicationUpstream", "properties": {"egressPolicy": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaEgressPolicy", "description": "Optional. Routing policy information."}, "network": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplicationUpstreamNetwork", "description": "Network to forward traffic to."}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplicationUpstreamNetwork": {"description": "Network to forward traffic to.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplicationUpstreamNetwork", "properties": {"name": {"description": "Required. Network name is of the format: `projects/{project}/global/networks/{network}", "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaEgressPolicy": {"description": "Routing policy information.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaEgressPolicy", "properties": {"regions": {"description": "Required. List of the regions where the application sends traffic.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaEndpointMatcher": {"description": "EndpointMatcher contains the information of the endpoint that will match the application.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaEndpointMatcher", "properties": {"hostname": {"description": "Required. Hostname of the application.", "type": "string"}, "ports": {"description": "Optional. Ports of the application.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaHub": {"description": "The Hub message contains information pertaining to the regional data path deployments.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaHub", "properties": {"internetGateway": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaInternetGateway", "description": "Optional. Internet Gateway configuration."}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaInternetGateway": {"description": "Represents the Internet Gateway configuration.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaInternetGateway", "properties": {"assignedIps": {"description": "Output only. List of IP addresses assigned to the Cloud NAT.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse": {"description": "Message for response to listing Applications.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaListApplicationsResponse", "properties": {"applications": {"description": "A list of BeyondCorp Application in the project.", "items": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaApplication"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse": {"description": "Message for response to listing SecurityGateways.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaListSecurityGatewaysResponse", "properties": {"nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "securityGateways": {"description": "A list of BeyondCorp SecurityGateway in the project.", "items": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway"}, "type": "array"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway": {"description": "The information about a security gateway resource.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGateway", "properties": {"createTime": {"description": "Output only. Timestamp when the resource was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "delegatingServiceAccount": {"description": "Output only. Service account used for operations that involve resources in consumer projects.", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. An arbitrary user-provided name for the SecurityGateway. Cannot exceed 64 characters.", "type": "string"}, "externalIps": {"description": "Output only. IP addresses that will be used for establishing connection to the endpoints.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "hubs": {"additionalProperties": {"$ref": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaHub"}, "description": "Optional. Map of Hubs that represents regional data path deployment with GCP region as a key.", "type": "object"}, "name": {"description": "Identifier. Name of the resource.", "type": "string"}, "state": {"description": "Output only. The operational state of the SecurityGateway.", "enum": ["STATE_UNSPECIFIED", "CREATING", "UPDATING", "DELETING", "RUNNING", "DOWN", "ERROR"], "enumDescriptions": ["Default value. This value is unused.", "SecurityGateway is being created.", "SecurityGateway is being updated.", "SecurityGateway is being deleted.", "SecurityGateway is running.", "SecurityGateway is down and may be restored in the future.", "SecurityGateway encountered an error and is in an indeterministic state."], "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Timestamp when the resource was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGatewayOperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "GoogleCloudBeyondcorpSecuritygatewaysV1alphaSecurityGatewayOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudLocationListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "GoogleCloudLocationListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "GoogleCloudLocationLocation"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "GoogleCloudLocationLocation": {"description": "A resource that represents a Google Cloud location.", "id": "GoogleCloudLocationLocation", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "GoogleIamV1AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "GoogleIamV1AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "GoogleIamV1AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "GoogleIamV1AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "GoogleIamV1AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "GoogleIamV1Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "GoogleIamV1Binding", "properties": {"condition": {"$ref": "GoogleTypeExpr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "GoogleIamV1Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "GoogleIamV1Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "GoogleIamV1AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "GoogleIamV1Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleIamV1SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "GoogleIamV1SetIamPolicyRequest", "properties": {"policy": {"$ref": "GoogleIamV1Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "GoogleIamV1TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "GoogleIamV1TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleIamV1TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "GoogleIamV1TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningCancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "GoogleLongrunningCancelOperationRequest", "properties": {}, "type": "object"}, "GoogleLongrunningListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "GoogleLongrunningListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "GoogleLongrunningOperation"}, "type": "array"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "GoogleRpcStatus", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "GoogleTypeExpr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "GoogleTypeExpr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "ImageConfig": {"description": "ImageConfig defines the control plane images to run.", "id": "ImageConfig", "properties": {"stableImage": {"description": "The stable image that the remote agent will fallback to if the target image fails.", "type": "string"}, "targetImage": {"description": "The initial image the remote agent will attempt to run for the control plane.", "type": "string"}}, "type": "object"}, "ListAppGatewaysResponse": {"description": "Response message for BeyondCorp.ListAppGateways.", "id": "ListAppGatewaysResponse", "properties": {"appGateways": {"description": "A list of BeyondCorp AppGateways in the project.", "items": {"$ref": "AppGateway"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListConnectionsResponse": {"description": "Response message for BeyondCorp.ListConnections.", "id": "ListConnectionsResponse", "properties": {"connections": {"description": "A list of BeyondCorp Connections in the project.", "items": {"$ref": "Connection"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListConnectorsResponse": {"description": "Response message for BeyondCorp.ListConnectors.", "id": "ListConnectorsResponse", "properties": {"connectors": {"description": "A list of BeyondCorp Connectors in the project.", "items": {"$ref": "Connector"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "NotificationConfig": {"description": "NotificationConfig defines the mechanisms to notify instance agent.", "id": "NotificationConfig", "properties": {"pubsubNotification": {"$ref": "CloudPubSubNotificationConfig", "description": "Pub/Sub topic for Connector to subscribe and receive notifications from `projects/{project}/topics/{pubsub_topic}`"}}, "type": "object"}, "PrincipalInfo": {"description": "PrincipalInfo represents an Identity oneof.", "id": "PrincipalInfo", "properties": {"serviceAccount": {"$ref": "ServiceAccount", "description": "A GCP service account."}}, "type": "object"}, "RemoteAgentDetails": {"description": "RemoteAgentDetails reflects the details of a remote agent.", "id": "RemoteAgentDetails", "properties": {}, "type": "object"}, "ReportStatusRequest": {"description": "Request report the connector status.", "id": "ReportStatusRequest", "properties": {"requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "type": "string"}, "resourceInfo": {"$ref": "ResourceInfo", "description": "Required. Resource info of the connector."}, "validateOnly": {"description": "Optional. If set, validates request by executing a dry-run which would not alter the resource in any way.", "type": "boolean"}}, "type": "object"}, "ResolveConnectionsResponse": {"description": "Response message for BeyondCorp.ResolveConnections.", "id": "ResolveConnectionsResponse", "properties": {"connectionDetails": {"description": "A list of BeyondCorp Connections with details in the project.", "items": {"$ref": "ConnectionDetails"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results, or empty if there are no more results in the list.", "type": "string"}, "unreachable": {"description": "A list of locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ResolveInstanceConfigResponse": {"description": "Response message for BeyondCorp.ResolveInstanceConfig.", "id": "ResolveInstanceConfigResponse", "properties": {"instanceConfig": {"$ref": "ConnectorInstanceConfig", "description": "ConnectorInstanceConfig."}}, "type": "object"}, "ResourceInfo": {"description": "ResourceInfo represents the information/status of the associated resource.", "id": "ResourceInfo", "properties": {"id": {"description": "Required. Unique Id for the resource.", "type": "string"}, "resource": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Specific details for the resource.", "type": "object"}, "status": {"description": "Overall health status. Overall status is derived based on the status of each sub level resources.", "enum": ["HEALTH_STATUS_UNSPECIFIED", "HEALTHY", "UNHEALTHY", "UNRESPONSIVE", "DEGRADED"], "enumDescriptions": ["Health status is unknown: not initialized or failed to retrieve.", "The resource is healthy.", "The resource is unhealthy.", "The resource is unresponsive.", "Some sub-resources are UNHEALTHY."], "type": "string"}, "sub": {"description": "List of Info for the sub level resources.", "items": {"$ref": "ResourceInfo"}, "type": "array"}, "time": {"description": "The timestamp to collect the info. It is suggested to be set by the topmost level resource only.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ServiceAccount": {"description": "ServiceAccount represents a GCP service account.", "id": "ServiceAccount", "properties": {"email": {"description": "Email address of the service account.", "type": "string"}}, "type": "object"}, "Tunnelv1ProtoTunnelerError": {"description": "TunnelerError is an error proto for errors returned by the connection manager.", "id": "Tunnelv1ProtoTunnelerError", "properties": {"err": {"description": "Original raw error", "type": "string"}, "retryable": {"description": "retryable isn't used for now, but we may want to reuse it in the future.", "type": "boolean"}}, "type": "object"}, "Tunnelv1ProtoTunnelerInfo": {"description": "TunnelerInfo contains metadata about tunneler launched by connection manager.", "id": "Tunnelv1ProtoTunnelerInfo", "properties": {"backoffRetryCount": {"description": "backoff_retry_count stores the number of times the tunneler has been retried by tunManager for current backoff sequence. Gets reset to 0 if time difference between 2 consecutive retries exceeds backoffRetryResetTime.", "format": "uint32", "type": "integer"}, "id": {"description": "id is the unique id of a tunneler.", "type": "string"}, "latestErr": {"$ref": "Tunnelv1ProtoTunnelerError", "description": "latest_err stores the Error for the latest tunneler failure. Gets reset everytime the tunneler is retried by tunManager."}, "latestRetryTime": {"description": "latest_retry_time stores the time when the tunneler was last restarted.", "format": "google-datetime", "type": "string"}, "totalRetryCount": {"description": "total_retry_count stores the total number of times the tunneler has been retried by tunManager.", "format": "uint32", "type": "integer"}}, "type": "object"}}, "servicePath": "", "title": "BeyondCorp API", "version": "v1alpha", "version_module": true}