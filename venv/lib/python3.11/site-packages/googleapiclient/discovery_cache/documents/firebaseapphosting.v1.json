{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://firebaseapphosting.googleapis.com/", "batchPath": "batch", "canonicalName": "Firebase App Hosting", "description": "Firebase App Hosting streamlines the development and deployment of dynamic Next.js and Angular applications, offering built-in framework support, GitHub integration, and integration with other Firebase products. You can use this API to intervene in the Firebase App Hosting build process and add custom functionality not supported in our default Console & CLI flows, including triggering builds from external CI/CD workflows or deploying from pre-built container images. ", "discoveryVersion": "v1", "documentationLink": "https://firebase.google.com/docs/app-hosting", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "firebaseapphosting:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://firebaseapphosting.mtls.googleapis.com/", "name": "firebaseapphosting", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. Do not use this field. It is unsupported and is ignored unless explicitly documented otherwise. This is primarily for internal usage.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"backends": {"methods": {"create": {"description": "Creates a new backend in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends", "httpMethod": "POST", "id": "firebaseapphosting.projects.locations.backends.create", "parameterOrder": ["parent"], "parameters": {"backendId": {"description": "Required. Id of the backend. Also used as the service ID for Cloud Run, and as part of the default domain name.", "location": "query", "type": "string"}, "parent": {"description": "Required. A parent name of the form `projects/{project}/locations/{locationId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/backends", "request": {"$ref": "Backend"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}", "httpMethod": "DELETE", "id": "firebaseapphosting.projects.locations.backends.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If the client provided etag is out of date, delete will be returned FAILED_PRECONDITION error.", "location": "query", "type": "string"}, "force": {"description": "Optional. If set to true, any resources for this backend will also be deleted. Otherwise, any children resources will block deletion.", "location": "query", "type": "boolean"}, "name": {"description": "Required. Name of the resource in the format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated, without persisting the request or updating any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource in the format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Backend"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists backends in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to narrow down results to a preferred subset. Learn more about filtering in Google's [AIP 160 standard](https://google.aip.dev/160).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results. Supported fields are `name` and `createTime`. To specify descending order, append a `desc` suffix.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from the nextPageToken field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. A parent name of the form `projects/{project}/locations/{locationId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Optional. If true, the request returns soft-deleted resources that haven't been fully-deleted yet.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/backends", "response": {"$ref": "ListBackendsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the information for a single backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}", "httpMethod": "PATCH", "id": "firebaseapphosting.projects.locations.backends.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the backend is not found, a new backend will be created.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. The resource name of the backend. Format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the backend resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated, without persisting the request or updating any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Backend"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"builds": {"methods": {"create": {"description": "Creates a new build for a backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/builds", "httpMethod": "POST", "id": "firebaseapphosting.projects.locations.backends.builds.create", "parameterOrder": ["parent"], "parameters": {"buildId": {"description": "Required. Desired ID of the build being created.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent backend in the format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/builds", "request": {"$ref": "Build"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single build.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/builds/{buildsId}", "httpMethod": "DELETE", "id": "firebaseapphosting.projects.locations.backends.builds.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If the client provided etag is out of date, delete will be returned FAILED_PRECONDITION error.", "location": "query", "type": "string"}, "name": {"description": "Required. Name of the resource in the format: `projects/{project}/locations/{locationId}/backends/{backendId}/builds/{buildId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+/builds/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or deleting any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a build.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/builds/{buildsId}", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.builds.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource in the format: `projects/{project}/locations/{locationId}/backends/{backendId}/builds/{buildId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+/builds/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Build"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists builds in a given project, location, and backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/builds", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.builds.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to narrow down results to a preferred subset. Learn more about filtering in Google's [AIP 160 standard](https://google.aip.dev/160).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results. Supported fields are `name` and `createTime`. To specify descending order, append a `desc` suffix.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from the nextPageToken field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent backend in the form `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Optional. If true, the request returns soft-deleted resources that haven't been fully-deleted yet.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/builds", "response": {"$ref": "ListBuildsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "domains": {"methods": {"create": {"description": "Links a new domain to a backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/domains", "httpMethod": "POST", "id": "firebaseapphosting.projects.locations.backends.domains.create", "parameterOrder": ["parent"], "parameters": {"domainId": {"description": "Required. Id of the domain to create. Must be a valid domain name.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent backend in the format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/domains", "request": {"$ref": "Domain"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a single domain.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/domains/{domainsId}", "httpMethod": "DELETE", "id": "firebaseapphosting.projects.locations.backends.domains.delete", "parameterOrder": ["name"], "parameters": {"etag": {"description": "Optional. If the client provided etag is out of date, delete will be returned FAILED_PRECONDITION error.", "location": "query", "type": "string"}, "name": {"description": "Required. Name of the resource in the format: `projects/{project}/locations/{locationId}/backends/{backendId}/domains/{domainId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+/domains/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or deleting any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a domain.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/domains/{domainsId}", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.domains.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource in the format: `projects/{project}/locations/{locationId}/backends/{backendId}/domains/{domainId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+/domains/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Domain"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists domains of a backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/domains", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.domains.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to narrow down results to a preferred subset. Learn more about filtering in Google's [AIP 160 standard](https://google.aip.dev/160).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results. Supported fields are `name` and `createTime`. To specify descending order, append a `desc` suffix.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from the nextPageToken field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent backend in the format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Optional. If true, the request returns soft-deleted resources that haven't been fully-deleted yet.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/domains", "response": {"$ref": "ListDomainsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the information for a single domain.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/domains/{domainsId}", "httpMethod": "PATCH", "id": "firebaseapphosting.projects.locations.backends.domains.patch", "parameterOrder": ["name"], "parameters": {"allowMissing": {"description": "Optional. If set to true, and the domain is not found, a new domain will be created.", "location": "query", "type": "boolean"}, "name": {"description": "Identifier. The resource name of the domain, e.g. `/projects/p/locations/l/backends/b/domains/foo.com`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+/domains/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the Domain resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or modifying any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Domain"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "rollouts": {"methods": {"create": {"description": "Creates a new rollout for a backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/rollouts", "httpMethod": "POST", "id": "firebaseapphosting.projects.locations.backends.rollouts.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent backend in the format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "rolloutId": {"description": "Optional. Desired ID of the rollout being created.", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated and default values populated, without persisting the request or creating any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/rollouts", "request": {"$ref": "Rollout"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets information about a rollout.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/rollouts/{rolloutsId}", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.rollouts.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource in the format: `projects/{project}/locations/{locationId}/backends/{backendId}/rollouts/{rolloutId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+/rollouts/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Rollout"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists rollouts for a backend.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/rollouts", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.rollouts.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. A filter to narrow down results to a preferred subset. Learn more about filtering in Google's [AIP 160 standard](https://google.aip.dev/160).", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Hint for how to order the results. Supported fields are `name` and `createTime`. To specify descending order, append a `desc` suffix.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token received from the nextPageToken field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent backend in the format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+$", "required": true, "type": "string"}, "showDeleted": {"description": "Optional. If true, the request returns soft-deleted resources that haven't been fully-deleted yet.", "location": "query", "type": "boolean"}}, "path": "v1/{+parent}/rollouts", "response": {"$ref": "ListRolloutsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "traffic": {"methods": {"get": {"description": "Gets information about a backend's traffic.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/traffic", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.backends.traffic.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of the resource in the format: `projects/{project}/locations/{locationId}/backends/{backendId}/traffic`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+/traffic$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Traffic"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a backend's traffic.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/backends/{backendsId}/traffic", "httpMethod": "PATCH", "id": "firebaseapphosting.projects.locations.backends.traffic.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name of the backend's traffic. Format: `projects/{project}/locations/{locationId}/backends/{backendId}/traffic`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/backends/[^/]+/traffic$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and t he request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask is used to specify the fields to be overwritten in the traffic resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}, "validateOnly": {"description": "Optional. Indicates that the request should be validated, without persisting the request or updating any resources.", "location": "query", "type": "boolean"}}, "path": "v1/{+name}", "request": {"$ref": "Traffic"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "firebaseapphosting.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "firebaseapphosting.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "firebaseapphosting.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250814", "rootUrl": "https://firebaseapphosting.googleapis.com/", "schemas": {"Backend": {"description": "A backend is the primary resource of App Hosting.", "id": "Backend", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.", "type": "object"}, "appId": {"description": "Optional. The [ID of a Web App](https://firebase.google.com/docs/reference/firebase-management/rest/v1beta1/projects.webApps#WebApp.FIELDS.app_id) associated with the backend.", "type": "string"}, "codebase": {"$ref": "Codebase", "description": "Optional. If specified, the connection to an external source repository to watch for event-driven updates to the backend."}, "createTime": {"description": "Output only. Time at which the backend was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. Time at which the backend was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. Human-readable name. 63 character limit.", "type": "string"}, "environment": {"description": "Optional. The environment name of the backend, used to load environment variables from environment specific configuration.", "type": "string"}, "etag": {"description": "Output only. Server-computed checksum based on other values; may be sent on update or delete to ensure operation is done on expected resource.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that can be used to organize and categorize objects.", "type": "object"}, "managedResources": {"description": "Output only. A list of the resources managed by this backend.", "items": {"$ref": "ManagedResource"}, "readOnly": true, "type": "array"}, "mode": {"deprecated": true, "description": "Optional. Deprecated: Use `environment` instead.", "type": "string"}, "name": {"description": "Identifier. The resource name of the backend. Format: `projects/{project}/locations/{locationId}/backends/{backendId}`.", "type": "string"}, "reconciling": {"description": "Output only. A field that, if true, indicates that the system is working to make adjustments to the backend during a LRO.", "readOnly": true, "type": "boolean"}, "serviceAccount": {"description": "Required. The name of the service account used for Cloud Build and Cloud Run. Should have the role roles/firebaseapphosting.computeRunner or equivalent permissions.", "type": "string"}, "servingLocality": {"description": "Required. Immutable. Specifies how App Hosting will serve the content for this backend. It will either be contained to a single region (REGIONAL_STRICT) or allowed to use App Hosting's global-replicated serving infrastructure (GLOBAL_ACCESS).", "enum": ["SERVING_LOCALITY_UNSPECIFIED", "REGIONAL_STRICT", "GLOBAL_ACCESS"], "enumDescriptions": ["Unspecified. Will return an error if used.", "In this mode, App Hosting serves your backend's content from your chosen parent region. App Hosting only maintains data and serving infrastructure in that chosen region and does not replicate your data to other regions.", "In this mode, App Hosting serves your backend's content from multiple points-of-presence (POP) across the globe. App Hosting replicates your backend's configuration and cached data to these POPs and uses a global CDN to further decrease response latency. App Hosting-maintained Cloud Resources on your project, such as Cloud Run services, Cloud Build build, and Artifact Registry Images are still confined to your backend's parent region. Responses cached by the CDN may be stored in the POPs for the duration of the cache's TTL."], "type": "string"}, "uid": {"description": "Output only. System-assigned, unique identifier.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time at which the backend was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "uri": {"description": "Output only. The primary URI to communicate with the backend.", "readOnly": true, "type": "string"}}, "type": "object"}, "Build": {"description": "A single build for a backend, at a specific point codebase reference tag and point in time. Encapsulates several resources, including an Artifact Registry container image, a Cloud Build invocation that built the image, and the Cloud Run revision that uses that image.", "id": "Build", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.", "type": "object"}, "buildLogsUri": {"description": "Output only. The location of the [Cloud Build logs](https://cloud.google.com/build/docs/view-build-results) for the build process.", "readOnly": true, "type": "string"}, "config": {"$ref": "Config", "description": "Optional. Additional configuration of the service."}, "createTime": {"description": "Output only. Time at which the build was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. Time at which the build was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. Human-readable name. 63 character limit.", "type": "string"}, "environment": {"description": "Output only. The environment name of the backend when this build was created.", "readOnly": true, "type": "string"}, "errors": {"description": "Output only. A list of all errors that occurred during an App Hosting build.", "items": {"$ref": "Error"}, "readOnly": true, "type": "array"}, "etag": {"description": "Output only. Server-computed checksum based on other values; may be sent on update or delete to ensure operation is done on expected resource.", "readOnly": true, "type": "string"}, "image": {"description": "Output only. The Artifact Registry [container image](https://cloud.google.com/artifact-registry/docs/reference/rest/v1/projects.locations.repositories.dockerImages) URI, used by the Cloud Run [`revision`](https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services.revisions) for this build.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that can be used to organize and categorize objects.", "type": "object"}, "name": {"description": "Identifier. The resource name of the build. Format: `projects/{project}/locations/{locationId}/backends/{backendId}/builds/{buildId}`.", "type": "string"}, "reconciling": {"description": "Output only. A field that, if true, indicates that the build has an ongoing LRO.", "readOnly": true, "type": "boolean"}, "source": {"$ref": "BuildSource", "description": "Required. Immutable. The source for the build."}, "state": {"description": "Output only. The state of the build.", "enum": ["STATE_UNSPECIFIED", "BUILDING", "BUILT", "DEPLOYING", "READY", "FAILED"], "enumDescriptions": ["The build is in an unknown state.", "The build is building.", "The build has completed and is awaiting the next step. This may move to DEPLOYING once App Hosting starts to set up infrastructure.", "The infrastructure for this build is being set up.", "The infrastructure for this build is ready. The build may or may not be serving traffic - see `Backend.traffic` for the current state, or `Backend.traffic_statuses` for the desired state.", "The build has failed."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-assigned, unique identifier.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time at which the build was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "BuildSource": {"description": "The source for the build.", "id": "BuildSource", "properties": {"codebase": {"$ref": "CodebaseSource", "description": "A codebase source."}, "container": {"$ref": "ContainerSource", "description": "An Artifact Registry container image source."}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "Codebase": {"description": "The connection to an external source repository to watch for event-driven updates to the backend.", "id": "Codebase", "properties": {"repository": {"description": "Required. The resource name for the Developer Connect [`gitRepositoryLink`](https://cloud.google.com/developer-connect/docs/api/reference/rest/v1/projects.locations.connections.gitRepositoryLinks) connected to this backend, in the format: `projects/{project}/locations/{location}/connections/{connection}/gitRepositoryLinks/{repositoryLink}` The connection for the `gitRepositoryLink` must made be using the Firebase App Hosting GitHub App via the Firebase Console.", "type": "string"}, "rootDirectory": {"description": "Optional. If `repository` is provided, the directory relative to the root of the repository to use as the root for the deployed web app.", "type": "string"}}, "type": "object"}, "CodebaseSource": {"description": "A codebase source, representing the state of the codebase that the build will be created at.", "id": "CodebaseSource", "properties": {"author": {"$ref": "UserMetadata", "description": "Output only. The author contained in the metadata of a version control change.", "readOnly": true}, "branch": {"description": "The branch in the codebase to build from, using the latest commit.", "type": "string"}, "commit": {"description": "The commit in the codebase to build from.", "type": "string"}, "commitMessage": {"description": "Output only. The message of a codebase change.", "readOnly": true, "type": "string"}, "commitTime": {"description": "Output only. The time the change was made.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Output only. The human-friendly name to use for this Codebase when displaying a build. We use the first eight characters of the SHA-1 hash for GitHub.com.", "readOnly": true, "type": "string"}, "hash": {"description": "Output only. The full SHA-1 hash of a Git commit, if available.", "readOnly": true, "type": "string"}, "uri": {"description": "Output only. A URI linking to the codebase on an hosting provider's website. May not be valid if the commit has been rebased or force-pushed out of existence in the linked repository.", "readOnly": true, "type": "string"}}, "type": "object"}, "Config": {"description": "Additional configuration of the backend for this build.", "id": "Config", "properties": {"env": {"description": "Optional. Environment variables for this build.", "items": {"$ref": "EnvironmentVariable"}, "type": "array"}, "runConfig": {"$ref": "RunConfig", "description": "Optional. Additional configuration of the Cloud Run [`service`](https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services#resource:-service)."}}, "type": "object"}, "ContainerSource": {"description": "The URI of an Artifact Registry [container image](https://cloud.google.com/artifact-registry/docs/reference/rest/v1/projects.locations.repositories.dockerImages) to use as the build source.", "id": "ContainerSource", "properties": {"image": {"description": "Required. A URI representing a container for the backend to use.", "type": "string"}}, "type": "object"}, "CustomDomainOperationMetadata": {"description": "Additional metadata for operations on custom domains.", "id": "CustomDomainOperationMetadata", "properties": {"certState": {"description": "Output only. The custom domain's `CertState`, which must be `CERT_ACTIVE` for the create operations to complete.", "enum": ["CERT_STATE_UNSPECIFIED", "CERT_PREPARING", "CERT_VALIDATING", "CERT_PROPAGATING", "CERT_ACTIVE", "CERT_EXPIRING_SOON", "CERT_EXPIRED"], "enumDescriptions": ["The certificate's state is unspecified. The message is invalid if this is unspecified.", "The initial state of every certificate, represents App Hosting's intent to create a certificate before requests to a Certificate Authority are made.", "App Hosting is validating whether a domain name's DNS records are in a state that allow certificate creation on its behalf.", "The certificate was recently created, and needs time to propagate in App Hosting's load balancers.", "The certificate is active, providing secure connections for the domain names it represents.", "The certificate is expiring, all domain names on it will be given new certificates.", "The certificate has expired. App Hosting can no longer serve secure content on your domain name."], "readOnly": true, "type": "string"}, "hostState": {"description": "Output only. The custom domain's `HostState`, which must be `HOST_ACTIVE` for Create operations of the domain name this `CustomDomain` refers toto complete.", "enum": ["HOST_STATE_UNSPECIFIED", "HOST_UNHOSTED", "HOST_UNREACHABLE", "HOST_NON_FAH", "HOST_CONFLICT", "HOST_WRONG_SHARD", "HOST_ACTIVE"], "enumDescriptions": ["Your custom domain's host state is unspecified. The message is invalid if this is unspecified.", "Your custom domain isn't associated with any IP addresses.", "Your custom domain can't be reached. App Hosting services' DNS queries to find your domain's IP addresses resulted in errors. See your `CustomDomainStatus`'s `issues` field for more details.", "Your domain has only IP addresses that don't ultimately resolve to App Hosting.", "Your domain has IP addresses that resolve to both App Hosting and to other services. To ensure consistent results, remove `A` and `AAAA` records related to non-App-Hosting services.", "Your domain has IP addresses that resolve to an incorrect instance of the App Hosting Data Plane. App Hosting has multiple data plane instances to ensure high availability. The SSL certificate that App Hosting creates for your domain is only available on its assigned instance. If your domain's IP addresses resolve to an incorrect instance, App Hosting won't be able to serve secure content on it.", "All requests against your domain are served by App Hosting, via your domain's assigned shard. If the custom domain's `OwnershipState` is also `OWNERSHIP_ACTIVE`, App Hosting serves its backend's content on requests for the domain."], "readOnly": true, "type": "string"}, "issues": {"description": "Output only. A list of issues that are currently preventing the operation from completing. These are generally DNS-related issues encountered when querying a domain's records or attempting to mint an SSL certificate.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "liveMigrationSteps": {"description": "Output only. A list of steps that the user must complete to migrate their domain to App Hosting without downtime.", "items": {"$ref": "LiveMigrationStep"}, "readOnly": true, "type": "array"}, "ownershipState": {"description": "Output only. The custom domain's `OwnershipState`, which must be `OWNERSHIP_ACTIVE` for the create operations to complete.", "enum": ["OWNERSHIP_STATE_UNSPECIFIED", "OWNERSHIP_MISSING", "OWNERSHIP_UNREACHABLE", "OWNERSHIP_MISMATCH", "OWNERSHIP_CONFLICT", "OWNERSHIP_PENDING", "OWNERSHIP_ACTIVE"], "enumDescriptions": ["Your custom domain's ownership state is unspecified. This should never happen.", "Your custom domain's domain has no App-Hosting-related ownership records; no backend is authorized to serve on the domain in this Origin shard.", "Your custom domain can't be reached. App Hosting services' DNS queries to find your domain's ownership records resulted in errors. See your `CustomDomainStatus`'s `issues` field for more details.", "Your custom domain is owned by another App Hosting custom domain. Remove the conflicting records and replace them with records for your current custom domain.", "Your custom domain has conflicting `TXT` records that indicate ownership by both your current custom domain one or more others. Remove the extraneous ownership records to grant the current custom domain ownership.", "Your custom domain's DNS records are configured correctly. App Hosting will transfer ownership of your domain to this custom domain within 24 hours.", "Your custom domain owns its domain."], "readOnly": true, "type": "string"}, "quickSetupUpdates": {"description": "Output only. A set of DNS record updates to perform, to allow App Hosting to serve secure content on the domain.", "items": {"$ref": "DnsUpdates"}, "readOnly": true, "type": "array"}}, "type": "object"}, "CustomDomainStatus": {"description": "The status of a custom domain's linkage to a backend.", "id": "CustomDomainStatus", "properties": {"certState": {"description": "Output only. Tracks SSL certificate status for the domain.", "enum": ["CERT_STATE_UNSPECIFIED", "CERT_PREPARING", "CERT_VALIDATING", "CERT_PROPAGATING", "CERT_ACTIVE", "CERT_EXPIRING_SOON", "CERT_EXPIRED"], "enumDescriptions": ["The certificate's state is unspecified. The message is invalid if this is unspecified.", "The initial state of every certificate, represents App Hosting's intent to create a certificate before requests to a Certificate Authority are made.", "App Hosting is validating whether a domain name's DNS records are in a state that allow certificate creation on its behalf.", "The certificate was recently created, and needs time to propagate in App Hosting's load balancers.", "The certificate is active, providing secure connections for the domain names it represents.", "The certificate is expiring, all domain names on it will be given new certificates.", "The certificate has expired. App Hosting can no longer serve secure content on your domain name."], "readOnly": true, "type": "string"}, "hostState": {"description": "Output only. Tracks whether a custom domain is detected as appropriately directing traffic to App Hosting.", "enum": ["HOST_STATE_UNSPECIFIED", "HOST_UNHOSTED", "HOST_UNREACHABLE", "HOST_NON_FAH", "HOST_CONFLICT", "HOST_WRONG_SHARD", "HOST_ACTIVE"], "enumDescriptions": ["Your custom domain's host state is unspecified. The message is invalid if this is unspecified.", "Your custom domain isn't associated with any IP addresses.", "Your custom domain can't be reached. App Hosting services' DNS queries to find your domain's IP addresses resulted in errors. See your `CustomDomainStatus`'s `issues` field for more details.", "Your domain has only IP addresses that don't ultimately resolve to App Hosting.", "Your domain has IP addresses that resolve to both App Hosting and to other services. To ensure consistent results, remove `A` and `AAAA` records related to non-App-Hosting services.", "Your domain has IP addresses that resolve to an incorrect instance of the App Hosting Data Plane. App Hosting has multiple data plane instances to ensure high availability. The SSL certificate that App Hosting creates for your domain is only available on its assigned instance. If your domain's IP addresses resolve to an incorrect instance, App Hosting won't be able to serve secure content on it.", "All requests against your domain are served by App Hosting, via your domain's assigned shard. If the custom domain's `OwnershipState` is also `OWNERSHIP_ACTIVE`, App Hosting serves its backend's content on requests for the domain."], "readOnly": true, "type": "string"}, "issues": {"description": "Output only. A list of issues with domain configuration. Allows users to self-correct problems with DNS records.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "ownershipState": {"description": "Output only. Tracks whether the backend is permitted to serve content on the domain, based off the domain's DNS records.", "enum": ["OWNERSHIP_STATE_UNSPECIFIED", "OWNERSHIP_MISSING", "OWNERSHIP_UNREACHABLE", "OWNERSHIP_MISMATCH", "OWNERSHIP_CONFLICT", "OWNERSHIP_PENDING", "OWNERSHIP_ACTIVE"], "enumDescriptions": ["Your custom domain's ownership state is unspecified. This should never happen.", "Your custom domain's domain has no App-Hosting-related ownership records; no backend is authorized to serve on the domain in this Origin shard.", "Your custom domain can't be reached. App Hosting services' DNS queries to find your domain's ownership records resulted in errors. See your `CustomDomainStatus`'s `issues` field for more details.", "Your custom domain is owned by another App Hosting custom domain. Remove the conflicting records and replace them with records for your current custom domain.", "Your custom domain has conflicting `TXT` records that indicate ownership by both your current custom domain one or more others. Remove the extraneous ownership records to grant the current custom domain ownership.", "Your custom domain's DNS records are configured correctly. App Hosting will transfer ownership of your domain to this custom domain within 24 hours.", "Your custom domain owns its domain."], "readOnly": true, "type": "string"}, "requiredDnsUpdates": {"description": "Output only. Lists the records that must added or removed to a custom domain's DNS in order to finish setup and start serving content. Field is present during onboarding. Also present after onboarding if one or more of the above states is not *_ACTIVE, indicating the domain's DNS records are in a bad state.", "items": {"$ref": "DnsUpdates"}, "readOnly": true, "type": "array"}}, "type": "object"}, "DnsRecord": {"description": "A representation of a DNS records for a domain. DNS records are resource records that define how systems and services should behave when handling requests for a domain. For example, when you add `A` records to your domain's DNS records, you're informing other systems (such as your users' web browsers) to contact those IPv4 addresses to retrieve resources relevant to your domain (such as your App Hosting files).", "id": "DnsRecord", "properties": {"domainName": {"description": "Output only. The domain the record pertains to, e.g. `foo.bar.com.`.", "readOnly": true, "type": "string"}, "rdata": {"description": "Output only. The data of the record. The meaning of the value depends on record type: - A and AAAA: IP addresses for the domain. - CNAME: Another domain to check for records. - TXT: Arbitrary text strings associated with the domain. App Hosting uses TXT records to determine which Firebase projects have permission to act on the domain's behalf. - CAA: The record's flags, tag, and value, e.g. `0 issue \"pki.goog\"`.", "readOnly": true, "type": "string"}, "relevantState": {"description": "Output only. An enum that indicates which state(s) this DNS record applies to. Populated for all records with an `ADD` or `REMOVE` required action.", "items": {"enum": ["CUSTOM_DOMAIN_STATE_UNSPECIFIED", "HOST_STATE", "OWNERSHIP_STATE", "CERT_STATE"], "enumDescriptions": ["This message is invalid if this is unspecified.", "The custom domain's host state.", "The custom domain's ownership state.", "The custom domain's certificate state."], "type": "string"}, "readOnly": true, "type": "array"}, "requiredAction": {"description": "Output only. An enum that indicates the a required action for this record. Populated when the record is part of a required change in a `DnsUpdates` `discovered` or `desired` record set.", "enum": ["NONE", "ADD", "REMOVE"], "enumDescriptions": ["No action necessary.", "Add this record to your DNS records.", "Remove this record from your DNS records."], "readOnly": true, "type": "string"}, "type": {"description": "Output only. The record's type, which determines what data the record contains.", "enum": ["TYPE_UNSPECIFIED", "A", "CNAME", "TXT", "AAAA", "CAA"], "enumDescriptions": ["The record's type is unspecified. The message is invalid if this is unspecified.", "An `A` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). A records determine which IPv4 addresses a domain directs traffic towards.", "A `CNAME` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). `CNAME` or Canonical Name records map a domain to a different, canonical domain. If a `CNAME` record is present, it should be the only record on the domain.", "A `TXT` record, as defined in [RFC 1035](https://tools.ietf.org/html/rfc1035). `TXT` records hold arbitrary text data on a domain. Hosting uses `TXT` records to establish which Firebase Project has permission to act on a domain.", "An AAAA record, as defined in [RFC 3596](https://tools.ietf.org/html/rfc3596) AAAA records determine which IPv6 addresses a domain directs traffic towards.", "A CAA record, as defined in [RFC 6844](https://tools.ietf.org/html/rfc6844). CAA, or Certificate Authority Authorization, records determine which Certificate Authorities (SSL certificate minting organizations) are authorized to mint a certificate for the domain. App Hosting uses `pki.goog` as its primary CA. CAA records cascade. A CAA record on `foo.com` also applies to `bar.foo.com` unless `bar.foo.com` has its own set of CAA records. CAA records are optional. If a domain and its parents have no CAA records, all CAs are authorized to mint certificates on its behalf. In general, App Hosting only asks you to modify CAA records when doing so is required to unblock SSL cert creation."], "readOnly": true, "type": "string"}}, "type": "object"}, "DnsRecordSet": {"description": "A set of DNS records relevant to the setup and maintenance of a custom domain in App Hosting.", "id": "DnsRecordSet", "properties": {"checkError": {"$ref": "Status", "description": "Output only. An error App Hosting services encountered when querying your domain's DNS records. Note: App Hosting ignores `NXDOMAIN` errors, as those generally just mean that a domain name hasn't been set up yet.", "readOnly": true}, "domainName": {"description": "Output only. The domain name the record set pertains to.", "readOnly": true, "type": "string"}, "records": {"description": "Output only. Records on the domain.", "items": {"$ref": "DnsRecord"}, "readOnly": true, "type": "array"}}, "type": "object"}, "DnsUpdates": {"description": "A set of DNS record updates that you should make to allow App Hosting to serve secure content in response to requests against your domain. These updates present the current state of your domain's and related subdomains' DNS records when App Hosting last queried them, and the desired set of records that App Hosting needs to see before your custom domain can be fully active.", "id": "DnsUpdates", "properties": {"checkTime": {"description": "Output only. The last time App Hosting checked your custom domain's DNS records.", "format": "google-datetime", "readOnly": true, "type": "string"}, "desired": {"description": "Output only. The set of DNS records App Hosting needs in order to be able to serve secure content on the domain.", "items": {"$ref": "DnsRecordSet"}, "readOnly": true, "type": "array"}, "discovered": {"description": "Output only. The set of DNS records App Hosting discovered when inspecting a domain.", "items": {"$ref": "DnsRecordSet"}, "readOnly": true, "type": "array"}, "domainName": {"description": "Output only. The domain name the DNS updates pertain to.", "readOnly": true, "type": "string"}}, "type": "object"}, "Domain": {"description": "A domain name that is associated with a backend.", "id": "Domain", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Annotations as key value pairs.", "type": "object"}, "createTime": {"description": "Output only. Time at which the domain was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customDomainStatus": {"$ref": "CustomDomainStatus", "description": "Output only. Represents the state and configuration of a `CUSTOM` type domain. It is only present on Domains of that type.", "readOnly": true}, "deleteTime": {"description": "Output only. Time at which the domain was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "disabled": {"description": "Optional. Whether the domain is disabled. Defaults to false.", "type": "boolean"}, "displayName": {"description": "Optional. Mutable human-readable name for the domain. 63 character limit. e.g. `prod domain`.", "type": "string"}, "etag": {"description": "Output only. Server-computed checksum based on other values; may be sent on update or delete to ensure operation is done on expected resource.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Labels as key value pairs.", "type": "object"}, "name": {"description": "Identifier. The resource name of the domain, e.g. `/projects/p/locations/l/backends/b/domains/foo.com`", "type": "string"}, "reconciling": {"description": "Output only. A field that, if true, indicates that the build has an ongoing LRO.", "readOnly": true, "type": "boolean"}, "serve": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Optional. The serving behavior of the domain. If specified, the domain will serve content other than its backend's live content."}, "type": {"description": "Output only. The type of the domain.", "enum": ["TYPE_UNSPECIFIED", "DEFAULT", "CUSTOM"], "enumDescriptions": ["The type is unspecified (this should not happen).", "Default, App Hosting-provided and managed domains. These domains are created automatically with their parent backend and cannot be deleted except by deleting that parent, and cannot be moved to another backend. Default domains can be disabled via the `disabled` field.", "Custom, developer-owned domains. Custom Domains allow you to associate a domain you own with your App Hosting backend, and configure that domain to serve your backend's content."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-assigned, unique identifier.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time at which the domain was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "DomainOperationMetadata": {"description": "Represents the metadata of a long-running operation on domains.", "id": "DomainOperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "customDomainOperationMetadata": {"$ref": "CustomDomainOperationMetadata", "description": "Output only. Additional metadata for operations on custom domains.", "readOnly": true}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "EnvironmentVariable": {"description": "Environment variables for this build.", "id": "EnvironmentVariable", "properties": {"availability": {"description": "Optional. Where this variable should be made available. If left unspecified, will be available in both BUILD and BACKEND.", "items": {"enum": ["AVAILABILITY_UNSPECIFIED", "BUILD", "RUNTIME"], "enumDescriptions": ["The default value, unspecified, which is unused.", "This value is available when creating a Build from source code.", "This value is available at runtime within Cloud Run."], "type": "string"}, "type": "array"}, "secret": {"description": "A fully qualified secret version. The value of the secret will be accessed once while building the application and once per cold start of the container at runtime. The service account used by Cloud Build and by Cloud Run must each have the `secretmanager.versions.access` permission on the secret.", "type": "string"}, "value": {"description": "A plaintext value. This value is encrypted at rest, but all project readers can view the value when reading your backend configuration.", "type": "string"}, "variable": {"description": "Required. The name of the environment variable. - Must be a valid environment variable name (e.g. A-Z or underscores). - May not start with \"FIREBASE\" or \"GOOGLE\". - May not be a reserved environment variable for KNative/Cloud Run", "type": "string"}}, "type": "object"}, "Error": {"description": "The container for the rpc status and source for any errors found during the build process.", "id": "Error", "properties": {"cloudResource": {"description": "Output only. Resource link", "readOnly": true, "type": "string"}, "error": {"$ref": "Status", "description": "Output only. A status and (human readable) error message for the build, if in a `FAILED` state.", "readOnly": true}, "errorSource": {"description": "Output only. The source of the error for the build, if in a `FAILED` state.", "enum": ["ERROR_SOURCE_UNSPECIFIED", "CLOUD_BUILD", "CLOUD_RUN"], "enumDescriptions": ["Indicates that generic error occurred outside of the Cloud Build or Cloud Run processes, such as a pre-empted or user-canceled App Hosting Build.", "Indicates that the build failed during the Cloud Build process, such as a build timeout.", "Indicates that the build failed during the Cloud Run process, such as a service creation failure."], "readOnly": true, "type": "string"}}, "type": "object"}, "ListBackendsResponse": {"description": "Message for response to list backends", "id": "ListBackendsResponse", "properties": {"backends": {"description": "The list of backends", "items": {"$ref": "Backend"}, "type": "array"}, "nextPageToken": {"description": "A token identifying the next page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListBuildsResponse": {"description": "Message for response to list builds.", "id": "ListBuildsResponse", "properties": {"builds": {"description": "The list of builds.", "items": {"$ref": "Build"}, "type": "array"}, "nextPageToken": {"description": "A token identifying the next page of results the server should return.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListDomainsResponse": {"description": "Message for response to list domains.", "id": "ListDomainsResponse", "properties": {"domains": {"description": "Output only. The list of domains.", "items": {"$ref": "Domain"}, "readOnly": true, "type": "array"}, "nextPageToken": {"description": "Output only. A token identifying the next page of results the server should return.", "readOnly": true, "type": "string"}, "unreachable": {"description": "Output only. Locations that could not be reached.", "items": {"type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListRolloutsResponse": {"description": "Message for response to list rollouts.", "id": "ListRolloutsResponse", "properties": {"nextPageToken": {"description": "A token identifying the next page of results the server should return.", "type": "string"}, "rollouts": {"description": "The list of rollouts.", "items": {"$ref": "Rollout"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "LiveMigrationStep": {"description": "A set of updates including ACME challenges and DNS records that allow App Hosting to create an SSL certificate and establish project ownership for your domain name before you direct traffic to App Hosting servers. Use these updates to facilitate zero downtime migrations to App Hosting from other services. After you've made the recommended updates, check your custom domain's `ownershipState` and `certState`. To avoid downtime, they should be `OWNERSHIP_ACTIVE` and `CERT_ACTIVE`, respectively, before you update your `A` and `AAAA` records.", "id": "LiveMigrationStep", "properties": {"dnsUpdates": {"description": "Output only. DNS updates to facilitate your domain's zero-downtime migration to App Hosting.", "items": {"$ref": "DnsUpdates"}, "readOnly": true, "type": "array"}, "issues": {"description": "Output only. Issues that prevent the current step from completing.", "items": {"$ref": "Status"}, "readOnly": true, "type": "array"}, "relevantDomainStates": {"description": "Output only. One or more states from the `CustomDomainStatus` of the migrating domain that this step is attempting to make ACTIVE. For example, if the step is attempting to mint an SSL certificate, this field will include `CERT_STATE`.", "items": {"enum": ["CUSTOM_DOMAIN_STATE_UNSPECIFIED", "HOST_STATE", "OWNERSHIP_STATE", "CERT_STATE"], "enumDescriptions": ["This message is invalid if this is unspecified.", "The custom domain's host state.", "The custom domain's ownership state.", "The custom domain's certificate state."], "type": "string"}, "readOnly": true, "type": "array"}, "stepState": {"description": "Output only. The state of the live migration step, indicates whether you should work to complete the step now, in the future, or have already completed it.", "enum": ["STEP_STATE_UNSPECIFIED", "PREPARING", "PENDING", "INCOMPLETE", "PROCESSING", "COMPLETE"], "enumDescriptions": ["The step's state is unspecified. The message is invalid if this is unspecified.", "App Hosting doesn't have enough information to construct the step yet. Complete any prior steps and/or resolve this step's issue to proceed.", "The step's state is pending. Complete prior steps before working on a `PENDING` step.", "The step is incomplete. You should complete any `dnsUpdates` changes to complete it.", "You've done your part to update records and present challenges as necessary. App Hosting is now completing background processes to complete the step, e.g. minting an SSL cert for your domain.", "The step is complete. You've already made the necessary changes to your domain and/or prior hosting service to advance to the next step. Once all steps are complete, App Hosting is ready to serve secure content on your domain."], "readOnly": true, "type": "string"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "ManagedResource": {"description": "An external resource managed by App Hosting on the project.", "id": "ManagedResource", "properties": {"runService": {"$ref": "RunService", "description": "A Cloud Run [`service`](https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services#resource:-service), managed by App Hosting."}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of a long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The time the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. The time the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have been cancelled successfully have Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Redirect": {"description": "Specifies redirect behavior for a domain.", "id": "Redirect", "properties": {"status": {"description": "Optional. The status code to use in a redirect response. Must be a valid HTTP 3XX status code. Defaults to 302 if not present.", "format": "int64", "type": "string"}, "uri": {"description": "Required. The URI of the redirect's intended destination. This URI will be prepended to the original request path. URI without a scheme are assumed to be HTTPS.", "type": "string"}}, "type": "object"}, "Rollout": {"description": "A single rollout of a build for a backend.", "id": "Rollout", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.", "type": "object"}, "build": {"description": "Immutable. The name of a build that already exists. It doesn't have to be built; a rollout will wait for a build to be ready before updating traffic.", "type": "string"}, "createTime": {"description": "Output only. Time at which the rollout was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteTime": {"description": "Output only. Time at which the rollout was deleted.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Optional. Human-readable name. 63 character limit.", "type": "string"}, "error": {"$ref": "Status", "description": "Output only. A status and (human readable) error message for the rollout, if in a `FAILED` state.", "readOnly": true}, "etag": {"description": "Output only. Server-computed checksum based on other values; may be sent on update or delete to ensure operation is done on expected resource.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that can be used to organize and categorize objects.", "type": "object"}, "name": {"description": "Identifier. The resource name of the rollout. Format: `projects/{project}/locations/{locationId}/backends/{backendId}/rollouts/{rolloutId}`.", "type": "string"}, "reconciling": {"description": "Output only. A field that, if true, indicates that the Rollout currently has an LRO.", "readOnly": true, "type": "boolean"}, "state": {"description": "Output only. The state of the rollout.", "enum": ["STATE_UNSPECIFIED", "QUEUED", "PENDING_BUILD", "PROGRESSING", "PAUSED", "SUCCEEDED", "FAILED", "CANCELLED"], "enumDescriptions": ["The rollout is in an unknown state.", "The rollout is waiting for actuation to begin. This may be because it is waiting on another rollout to complete.", "The rollout is waiting for the build process to complete, which builds the code and sets up the underlying infrastructure.", "The rollout has started and is actively modifying traffic.", "The rollout has been paused due to either being manually paused or a PAUSED stage. This should be set while `paused = true`.", "The rollout has completed.", "The rollout has failed. See error for more information.", "The rollout has been cancelled."], "readOnly": true, "type": "string"}, "uid": {"description": "Output only. System-assigned, unique identifier.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time at which the rollout was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "RolloutPolicy": {"description": "The policy for how automatic builds and rollouts are triggered and rolled out.", "id": "RolloutPolicy", "properties": {"codebaseBranch": {"description": "If set, specifies a branch that triggers a new build to be started with this policy. Otherwise, no automatic rollouts will happen.", "type": "string"}, "disabled": {"description": "Optional. A flag that, if true, prevents automatic rollouts from being created via this RolloutPolicy.", "type": "boolean"}, "disabledTime": {"description": "Output only. If `disabled` is set, the time at which the automatic rollouts were disabled.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "RunConfig": {"description": "Additional configuration to apply to the Cloud Run [`service`](https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services#resource:-service).", "id": "RunConfig", "properties": {"concurrency": {"description": "Optional. Maximum number of requests that each Cloud Run instance can receive. By default, each instance can receive Cloud Run's default of up to 80 requests at the same time. Concurrency can be set to any integer value up to 1000.", "format": "int32", "type": "integer"}, "cpu": {"description": "Optional. Number of CPUs used for each serving instance. By default, cpu defaults to the Cloud Run's default of 1.0. CPU can be set to value 1, 2, 4, 6, or 8 CPUs, and for less than 1 CPU, a value from 0.08 to less than 1.00, in increments of 0.01. If you set a value of less than 1 CPU, you must set concurrency to 1, and CPU will only be allocated during request processing. Increasing CPUs limit may require increase in memory limits: - 4 CPUs: at least 2 GiB - 6 CPUs: at least 4 GiB - 8 CPUs: at least 4 GiB", "format": "float", "type": "number"}, "maxInstances": {"description": "Optional. Number of Cloud Run instances to maintain at maximum for each revision. By default, each Cloud Run [`service`](https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services#resource:-service) scales out to Cloud Run's default of a maximum of 100 instances. The maximum max_instances limit is based on your quota. See https://cloud.google.com/run/docs/configuring/max-instances#limits.", "format": "int32", "type": "integer"}, "memoryMib": {"description": "Optional. Amount of memory allocated for each serving instance in MiB. By default, memory defaults to the Cloud Run's default where each instance is allocated 512 MiB of memory. Memory can be set to any integer value between 128 to 32768. Increasing memory limit may require increase in CPUs limits: - Over 4 GiB: at least 2 CPUs - Over 8 GiB: at least 4 CPUs - Over 16 GiB: at least 6 CPUs - Over 24 GiB: at least 8 CPUs", "format": "int32", "type": "integer"}, "minInstances": {"description": "Optional. Number of Cloud Run instances to maintain at minimum for each Cloud Run Service. By default, there are no minimum. Even if the service splits traffic across multiple revisions, the total number of instances for a service will be capped at this value.", "format": "int32", "type": "integer"}}, "type": "object"}, "RunService": {"description": "A managed Cloud Run [`service`](https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services#resource:-service).", "id": "RunService", "properties": {"service": {"description": "Optional. The name of the Cloud Run [`service`](https://cloud.google.com/run/docs/reference/rest/v2/projects.locations.services#resource:-service), in the format: `projects/{project}/locations/{location}/services/{serviceId}`", "type": "string"}}, "type": "object"}, "ServingBehavior": {"description": "Indicates whether App Hosting will serve content on the domain.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"redirect": {"$ref": "Redirect", "description": "Optional. Redirect behavior for a domain, if provided."}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "Traffic": {"description": "Controls traffic configuration for the backend.", "id": "Traffic", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that may be set by external tools to store and arbitrary metadata. They are not queryable and should be preserved when modifying objects.", "type": "object"}, "createTime": {"description": "Output only. Time at which the backend was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "current": {"$ref": "TrafficSet", "description": "Output only. Current state of traffic allocation for the backend. When setting `target`, this field may differ for some time until the desired state is reached.", "readOnly": true}, "etag": {"description": "Output only. Server-computed checksum based on other values; may be sent on update or delete to ensure operation is done on expected resource.", "readOnly": true, "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. Unstructured key value map that can be used to organize and categorize objects.", "type": "object"}, "name": {"description": "Identifier. The resource name of the backend's traffic. Format: `projects/{project}/locations/{locationId}/backends/{backendId}/traffic`.", "type": "string"}, "reconciling": {"description": "Output only. A field that, if true, indicates that the system is working to make the backend's `current` match the requested `target` list.", "readOnly": true, "type": "boolean"}, "rolloutPolicy": {"$ref": "RolloutPolicy", "description": "A rollout policy specifies how new builds and automatic deployments are created."}, "target": {"$ref": "TrafficSet", "description": "Set to manually control the desired traffic for the backend. This will cause `current` to eventually match this value. The percentages must add up to 100%."}, "uid": {"description": "Output only. System-assigned, unique identifier.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time at which the backend was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "TrafficSet": {"description": "A list of traffic splits that together represent where traffic is being routed.", "id": "TrafficSet", "properties": {"splits": {"description": "Required. The list of traffic splits.", "items": {"$ref": "TrafficSplit"}, "type": "array"}}, "type": "object"}, "TrafficSplit": {"description": "The traffic allocation for the backend.", "id": "TrafficSplit", "properties": {"build": {"description": "Required. The build that traffic is being routed to.", "type": "string"}, "percent": {"description": "Required. The percentage of traffic to send to the build. Currently must be 100% or 0%.", "format": "int32", "type": "integer"}}, "type": "object"}, "UserMetadata": {"description": "Version control metadata for a user associated with a resolved codebase. Currently assumes a Git user.", "id": "UserMetadata", "properties": {"displayName": {"description": "Output only. The 'name' field in a Git user's git.config. Required by Git.", "readOnly": true, "type": "string"}, "email": {"description": "Output only. The 'email' field in a Git user's git.config, if available.", "readOnly": true, "type": "string"}, "imageUri": {"description": "Output only. The URI of an image file associated with the user's account in an external source control provider, if available.", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Firebase App Hosting API", "version": "v1", "version_module": true}